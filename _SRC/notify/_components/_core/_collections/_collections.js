Factory.register("collections", function (sb) {
  var views = [];
  var Collections = [];

  // utility functions

  function registerView(registration) {
    views.push(registration);
  }

  function getFieldTypeIcon(name, fields, Blueprint) {
    if (fields[name].type) {
      switch (fields[name].type) {
        case "image":
          return "picture";

        case "type":
          return "caret square down";
      }
    }

    if (Blueprint[name]) {
      switch (Blueprint[name].type) {
        case "date":
          return "calendar alternate outline";

        case "int":
          return "hashtag";

        case "objectId":
          return "linkify";

        case "string":
          return "sort alphabet down";
      }
    }

    return "info";
  }

  // field utility functions

  function getTypeField(fields) {
    var typeField = false;

    _.each(fields, function (field, key) {
      if (field.type == "type") {
        // 			if (field.hasOwnProperty('type')) {

        typeField = {
          field: key,
          title: field.title,
          type: field.type,
        };
      }
    });

    if (typeField) {
      return typeField;
    } else {
      return {
        field: "type",
        title: "Type",
      };
    }
  }

  function getObjectTypeName(objectType, where) {
    /*
			!TODO: 	Ideally, we should build these human-readable
		 			names into the blueprints themselves.
		*/

    switch (objectType) {
      case "contacts":
        return "contact";

      case "companies":
        return "company";

      case "document":
        return "document";

      case "groups":
        switch (where.group_type) {
          case "Project":
            return "project";

          case "Schedule":
            return "schedule";

          case "Task":
            return "task";

          case "Team":
            return "team";

          default:
            return "item";
        }

      case "users":
        return "team member";

      default:
        return "item";
    }
  }

  // data functions

  function prepareTypeSelection(query, queryObj, options) {
    var typeField = getTypeField(options.fields);

    if (typeField) {
      // for 'type' fields not named 'type'
      delete queryObj.type;
      if (query.type > 0) {
        queryObj[typeField.field] = query.type;
      }
    } else if (query.type && query.type > 0) {
      queryObj.type = query.type;
    }

    if (options.subTypes) {
      return queryObj;
    }

    // do not get full type objs, since we already have them
    _.each(queryObj.childObjs, function (property, key) {
      if (options.fields[key] && options.fields[key].type === "type") {
        queryObj.childObjs[key] = "id";
      }
    });

    if (!queryObj.type && query.where && query.where.type) {
      queryObj.type = query.where.type;
    }

    return queryObj;
  }

  function get_data_query(
    options,
    query,
    page,
    types,
    subview,
    fromWithinSubview,
    range
  ) {
    function prepareRangeSelection(request, options) {
      // default to ranging over 'date_created' field
      var dateField = "date_created";
      if (options.rangeOver && options.fields[options.rangeOver]) {
        dateField = options.rangeOver;
      } else {
        // check for rangeOver field field
        _.each(options.fields, function (field, key) {
          if (field.rangeOver) {
            dateField = key;
          }
        });
      }

      if (range !== undefined && range !== false) {
        request[dateField] = range;
        return request;
      }

      if (!query.range) {
        return request;
      } else {
        request[dateField] = {
          type: "between",
          start: "",
          end: "",
        };
      }

      if (
        typeof query.range === "object" &&
        query.range.hasOwnProperty("start")
      ) {
        request[dateField].start = query.range.start
          .clone()
          .local()
          .startOf()
          .format("X");
        request[dateField].end = query.range.end
          .clone()
          .local()
          .add(1, "day")
          .startOf()
          .format("X");
      } else {
        switch (query.range) {
          case "today":
            request[dateField].start = moment()
              .local()
              .startOf("day")
              .format("X");
            request[dateField].end = moment().local().endOf("day").format("X");
            break;

          case "this_week":
            request[dateField].start = moment()
              .local()
              .startOf("week")
              .format("X");
            request[dateField].end = moment().local().endOf("week").format("X");
            break;

          case "past_3_months":
            request[dateField].start = moment()
              .local()
              .startOf("month")
              .subtract(3, "months")
              .format("X");
            request[dateField].end = moment()
              .local()
              .endOf("month")
              .format("X");
            break;

          case "next_3_months":
            request[dateField].type = "before";
            request[dateField].date = moment()
              .local()
              .endOf("month")
              .add(3, "months")
              .format("X");
            break;

          ///report case options
          case "last_30":
            request[dateField].start = moment()
              .local()
              .subtract(30, "days")
              .format("X");
            request[dateField].end = moment().local().format("X");
            break;

          case "this_month":
            request[dateField].start = moment()
              .local()
              .startOf("month")
              .format("X");
            request[dateField].end = moment()
              .local()
              .endOf("month")
              .format("X");
            break;

          case "this_quarter":
            request[dateField].start = moment()
              .local()
              .startOf("quarter")
              .format("X");
            request[dateField].end = moment()
              .local()
              .endOf("quarter")
              .format("X");
            break;

          case "this_year":
            request[dateField].start = moment()
              .local()
              .startOf("year")
              .format("X");
            request[dateField].end = moment().local().endOf("year").format("X");
            break;

          case "last_month":
            request[dateField].start = moment()
              .local()
              .subtract(1, "month")
              .startOf("month")
              .format("X");
            request[dateField].end = moment()
              .local()
              .subtract(1, "month")
              .endOf("month")
              .format("X");
            break;

          case "last_quarter":
            request[dateField].start = moment()
              .local()
              .subtract(1, "quarter")
              .startOf("quarter")
              .format("X");
            request[dateField].end = moment()
              .local()
              .subtract(1, "quarter")
              .endOf("quarter")
              .format("X");
            break;

          case "last_year":
            request[dateField].start = moment()
              .local()
              .subtract(1, "year")
              .startOf("year")
              .format("X");
            request[dateField].end = moment()
              .local()
              .subtract(1, "year")
              .endOf("year")
              .format("X");
            break;

          default:
            delete request[dateField];
            break;
        }
      }

      range = request[dateField];

      return request;
    }

    if (subview.getsDataByRange && !fromWithinSubview) {
      return callback([], { data: [] });
    }

    // Set up paging info
    var myPage = {
      count: true,
      page: 0,
      pageLength: options.pageLength,
      paged: true,
      sortCol: "date_created",
      sortDir: "desc",
      sortCast: "string",
    };
    if (!_.isEmpty(page)) {
      myPage.page = (page.page - 1) * myPage.pageLength;
      myPage.sortCol = page.sortCol;
      myPage.sortDir = page.sortDir;
      myPage.sortCast = page.sortCast;
    }

    //!TODO: refactor this into a single case
    if (options.where) {
      var queryObj = _.clone(options.where);

      queryObj.paged = myPage;

      if (queryObj.childObjs) {
        queryObj.childObjs = _.clone(queryObj.childObjs);

        if (queryObj.childObjs._agg) {
          queryObj.childObjs._agg = _.clone(queryObj.childObjs._agg);
        }
      }

      if (!_.isEmpty(query.tagged_with)) {
        queryObj.tagged_with = query.tagged_with;
      }

      if (query.filters) {
        _.each(query.filters, function (filter) {
          if (
            !_.isEmpty(filter.field) &&
            (!_.isEmpty(filter.value) ||
              (Number.isInteger(filter.value) && filter.value > 0)) &&
            !_.isEmpty(filter.type)
          ) {
            switch (filter.type) {
              case "contains":
                queryObj[filter.field] = {
                  type: "contains",
                  value: filter.value,
                };
                break;

              case "less_than":
                queryObj[filter.field] = {
                  type: "less_than",
                  value: filter.value,
                };
                break;

              case "greater_than":
                queryObj[filter.field] = {
                  type: "greater_than",
                  value: filter.value,
                };
                break;

              case "equal_to":
                queryObj[filter.field] = filter.value;
                break;
            }
          }
        });
      }

      if (query.search.isActive && !_.isEmpty(query.search.fields)) {
        queryObj.search = {
          fields: query.search.fields,
          value: query.search.search_term,
          type: "or",
        };

        // Turn on fuzzy searching when within a set
        if (
          typeof options.objectType === "string" &&
          options.objectType.startsWith("#")
        ) {
          // queryObj.search.fuzzySearch = true;
        }

        // Search uid value as well, if the search term is an integer.
        if (!isNaN(parseInt(query.search.search_term))) {
          queryObj.search.fields.push("object_uid");
        }

        queryObj.search.fields = _.uniq(queryObj.search.fields);
      }

      if (query.type) {
        queryObj.type = query.type;
      }
      if (query.role && query.role != 0) {
        queryObj.role = query.role;
      }

      if (query.state) {
        queryObj.state = query.state;
      }

      if (query.archive) {
        queryObj.archive = true;
      }

      if (query.templates) {
        queryObj.is_template = 1;
      } else {
        queryObj.is_template = {
          type: "not_equal",
          value: 1,
        };
      }
      queryObj = prepareTypeSelection(query, queryObj, options);
      queryObj = prepareRangeSelection(queryObj, options);

      // set the pool in the tagged_with filter
      if (Number.isInteger(options.pool) && options.pool > 0) {
        if (!Array.isArray(queryObj.tagged_with)) {
          queryObj.tagged_with = [];
        }

        queryObj.tagged_with.push(options.pool);
      }

      if (parseInt(options.tool) > 0) {
        if (!Array.isArray(queryObj.tagged_with)) {
          queryObj.tagged_with = [];
        }
        queryObj.tagged_with.push(options.tool);
      }
      if (parseInt(options.parent) > 0) {
        if (!Array.isArray(queryObj.tagged_with)) {
          queryObj.tagged_with = [];
        }
        queryObj.tagged_with.push(options.parent);
      }
      if (parseInt(options.entity) > 0) {
        queryObj.parent = options.entity;
      }

      if (_.isArray(queryObj.tagged_with)) {
        queryObj.tagged_with = _.uniq(queryObj.tagged_with);
        queryObj.tagged_with = _.compact(queryObj.tagged_with);
      }
    }

    // For tools in projects in instances opened through portals, where the parent instance is
    // not a portal-only instance, do not force filtering by user tag
    if (
      appConfig.state &&
      appConfig.state.portal > 0 && // if in a portal..
      appConfig &&
      appConfig.rootInstance &&
      !appConfig.rootInstance.is_portal && // ..and the root instance is not portal-only
      options.layer === "project"
    ) {
      queryObj._dont_force_portal_user_tag = true;
    }

    return queryObj;
  }

  function get_data(
    options,
    query,
    callback,
    page,
    types,
    subview,
    fromWithinSubview,
    range,
    bypassCache
  ) {
    function mergeTypeObjs(data, types) {
      _.each(options.fields, function (field, key) {
        if (field.type === "type") {
          _.each(data, function (item) {
            if (Number.isInteger(item[key])) {
              item[key] = _.findWhere(types, { id: item[key] });
            }
          });
        }
      });

      return data;
    }

    if (subview.getsDataByRange && !fromWithinSubview) {
      return callback([], { data: [] });
    }

    var queryObj = get_data_query(
      options,
      query,
      page,
      types,
      subview,
      fromWithinSubview,
      range
    );

    // If we are looking at a user-defined set in the list view,
    // only get name/status properties
    if (
      typeof options === "object" &&
      typeof options.objectType === "string" &&
      options.objectType.startsWith("#") &&
      subview.name === "list"
    ) {
      var bp = _.findWhere(appConfig.Types, {
        bp_name: options.objectType.substring(1),
      });
      if (typeof queryObj.childObjs === "object" && bp) {
        _.each(bp.blueprint, function (fieldDef, key) {
          if (key === "object_uid") {
            return;
          }
          switch (fieldDef.fieldType) {
            case "title":
            case "state":
            case "user":
            case "users":
            case "timer":
              break;

            default:
              delete queryObj.childObjs[key];
              break;
          }
        });
      }
    }

    //!TODO: refactor this into a single case
    if (options.query) {
      // Run custom query provided by use-case
      options.query(queryObj, function (data) {
        // For custom queries in groups, merge in the types for the views
        // so that we don't need to load in dup data
        if (options.objectType === "groups" && Array.isArray(types)) {
          _.each(data.data, function (record) {
            if (record && typeof record.type === "number") {
              record.type = _.findWhere(types, { id: record.type });
            }
          });
        }

        // Pass data back to view
        callback(data.data, data);
      });
    } else if (options.data && options.data.hasOwnProperty("obj")) {
      var data = {
        data: options.data.obj,
      };

      // Pass data back to view
      callback(data.data, data);
    } else if (options.where) {
      var type = options.objectType;
      if (options.entityTypes) {
        type = _.pluck(options.entityTypes, "bp_name");
        type = _.map(type, function (t) {
          return "#" + t;
        });
      }

      // Switch between sub-sets in sets
      if (!_.isEmpty(options.entityType) && queryObj.type) {
        var subSet = _.findWhere(types, { id: queryObj.type });
        if (subSet) {
          delete queryObj.type;
          type = "#" + subSet.bp_name;
        }
      }
      sb.data.db.obj.getWhere(
        type,
        _.clone(queryObj),
        function (data) {
          if (options.parseData) {
            data.data = mergeTypeObjs(data.data, types);

            options.parseData(
              data,
              function (data) {
                callback(data.data, data);
              },
              _.clone(queryObj),
              subview,
              range,
              types,
              options
            );
          } else {
            data.data = mergeTypeObjs(data.data, types);
            callback(data.data, data);
          }
        },
        bypassCache
      );
    } else if (query.tagged_with) {
      sb.data.db.obj.getWhere(
        options.objectType,
        { tagged_with: query.tagged_with },
        function (data) {
          callback(data.data, data);
        }
      );
    } else {
      sb.data.db.obj.getAll(
        options.objectType,
        function (data) {
          callback(data.data, data);
        },
        options.selection,
        myPage
      );
    }
  }

  function get_sum(options, query, callback, page, types, subview, field) {
    var queryObj = get_data_query(options, query, page, types, subview);

    delete queryObj.paged;
    delete queryObj.childObjs;

    sb.data.db.obj.sum(options.objectType, field, queryObj, function (resp) {
      if (options.fields[field].type === "timer") {
        sb.data.db.obj.sum(
          options.objectType,
          field + "_est",
          queryObj,
          function (est) {
            var r = {
              logged: resp,
              est: est,
            };

            callback(r);
          }
        );
      } else {
        callback(resp);
      }
    });
  }

  function get_pool_data(options, callback) {
    var selectionObj = {
      //!TODO: this is not limiting currently
      color: true,
      tag: true,
      name: true,
    };

    // if on the hq, show all tags
    if (options.layer === "hq") {
      var ret = [];

      sb.data.db.obj.getAll(
        "users",
        function (users) {
          ret = users;

          sb.data.db.obj.getWhere(
            "groups",
            {
              group_type: "Team",
              childObjs: {
                name: true,
                color: true,
                group_type: true,
              },
            },
            function (teams) {
              ret = _.union(ret, teams);

              sb.data.db.obj.getWhere(
                "groups",
                {
                  group_type: "Project",
                  childObjs: {
                    name: true,
                    color: true,
                    group_type: true,
                  },
                },
                function (projects) {
                  ret = _.union(ret, projects);

                  sb.data.db.obj.getAll(
                    "system_tags",
                    function (streams) {
                      ret = _.union(ret, streams);

                      callback(ret);
                    },
                    selectionObj
                  );
                }
              );
            }
          );
        },
        {
          fname: true,
          lname: true,
          color: true,
        }
      );

      // bypass if empty
    } else if (_.isEmpty(options.pools)) {
      callback([]);
    } else {
      sb.data.db.obj.getById(
        "system_tags",
        options.pools,
        function (streams) {
          callback(streams);
        },
        selectionObj
      );
    }
  }

  function get_chart_data(options, query, x, y, callback) {
    var request = {
      groupBy: "day",
      dateField: "date_created",
      dateRange: {
        start: "",
        end: "",
      },
      sumCast: "REAL",
      is_template: 0,
    };

    if (options.hasOwnProperty("chartDataSetup")) {
      if (options.chartDataSetup.hasOwnProperty("dateField")) {
        request.dateField = options.chartDataSetup.dateField;
      }

      if (options.chartDataSetup.hasOwnProperty("groupBy")) {
        request.groupBy = options.chartDataSetup.groupBy;
      }

      if (
        options.chartDataSetup.hasOwnProperty("group_type") &&
        options.chartDataSetup.group_type !== false
      ) {
        request.group_type = options.chartDataSetup.group_type;
      }

      if (
        options.chartDataSetup.hasOwnProperty("type") &&
        options.chartDataSetup.type !== false
      ) {
        request.type = options.chartDataSetup.type;
      } else {
        delete request.type;
      }

      if (
        options.chartDataSetup.hasOwnProperty("state") &&
        options.chartDataSetup.state !== false
      ) {
        request.state = options.chartDataSetup.state;
      } else {
        delete request.state;
      }

      if (!_.isEmpty(options.chartDataSetup.filters)) {
        _.each(options.chartDataSetup.filters, function (filterObj) {
          request[filterObj.field] = filterObj.value;
        });
      }

      if (options.chartDataSetup.hasOwnProperty("sumCast")) {
        request.sumCast = options.chartDataSetup.sumCast;
      }
    }

    if (
      typeof query.range === "object" &&
      query.range.hasOwnProperty("start")
    ) {
      request.dateRange.start = query.range.start.format(
        "YYYY-MM-DD HH:mm:ss.SS"
      );
      request.dateRange.end = query.range.end.format("YYYY-MM-DD HH:mm:ss.SS");
    } else {
      switch (query.range) {
        case "today":
          request.dateRange.start = moment()
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          request.dateRange.end = moment()
            .endOf("day")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          break;

        case "this_week":
          request.dateRange.start = moment()
            .startOf("week")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          request.dateRange.end = moment()
            .endOf("week")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          break;

        case "this_month":
          request.dateRange.start = moment()
            .startOf("month")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          request.dateRange.end = moment()
            .endOf("month")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          break;

        case "past_3_months":
          request.dateRange.start = moment()
            .startOf("month")
            .subtract(3, "months")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          request.dateRange.end = moment()
            .endOf("month")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          break;

        case "next_3_months":
          request.dateRange.start = moment()
            .startOf("month")
            .add(3, "months")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          request.dateRange.end = moment()
            .endOf("month")
            .add(3, "months")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          break;

        case "this_year":
          request.dateRange.start = moment()
            .startOf("year")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          request.dateRange.end = moment()
            .endOf("year")
            .format("YYYY-MM-DD HH:mm:ss.SS");
          break;
      }
    }

    var isOverTime = false;
    if (options.fields[x] && options.fields[x].type === "date") {
      isOverTime = true;
    }

    if (isOverTime) {
      sb.data.db.obj.getSum(options.objectType, y, request, function (data) {
        if (data == null) {
          data = [];
        } else {
          data = _.map(data, function (resp) {
            resp[0] = moment(resp[0]).local().format("YYYY-MM-DD HH:mm:ss");
            resp.grouped = moment(resp.grouped)
              .local()
              .format("YYYY-MM-DD HH:mm:ss");

            return resp;
          });
        }

        callback(data);
      });
    } else {
      request.groupOn = x;

      //!TESTING
      /*
			request.groupOn = {
				num: 'books'
				, div: 'students'
			};
			y = 'books';
*/

      sb.data.db.obj.getGroupSum(
        options.objectType,
        y,
        request,
        function (data) {
          if (data == null) {
            data = [];
          }

          callback(data);
        }
      );
    }
  }

  function get_counts(options, query, groupBy, dateField, callback) {
    var where = _.clone(query.where);
    delete where.childObjs;
    where[dateField] = {
      type: "between",
      start: "",
      end: "",
    };

    switch (query.range) {
      case "all_time":
        where[dateField].type = 'before';
        where[dateField].start = '';
        where[dateField].end = '';
        where[dateField].date = moment().endOf("day").format("X");
        break;
      case "today":
        where[dateField].start = moment().startOf("day").format("X");
        where[dateField].end = moment().endOf("day").format("X");
        break;

      case "this_week":
        where[dateField].start = moment().startOf("week").format("X");
        where[dateField].end = moment().endOf("week").format("X");
        break;

      case "this_month":
        where[dateField].start = moment().startOf("month").format("X");
        where[dateField].end = moment().endOf("month").format("X");
        break;

      case "past_3_months":
        where[dateField].start = moment()
          .startOf("month")
          .subtract(3, "months")
          .format("X");
        where[dateField].end = moment().endOf("month").format("X");
        break;

      case "next_3_months":
        where[dateField].start = moment()
          .startOf("month")
          .add(3, "months")
          .format("X");
        where[dateField].end = moment()
          .endOf("month")
          .add(3, "months")
          .format("X");
        break;

      case "this_year":
        where[dateField].start = moment().startOf("year").format("X");
        where[dateField].end = moment().endOf("year").format("X");
        break;
    }

    where = prepareTypeSelection(query, where, options);

    sb.data.db.obj.getCounts(
      options.objectType,
      groupBy,
      where,
      function (data) {
        if (data == null) {
          data = [];
        }

        callback(data);
      }
    );
  }

  // field views

  function FieldView(fieldName, fieldType, field, ui, obj, options) {
    var viewOptions = _.clone(this.ViewState);

    if (field.edit === false) {
      viewOptions = _.clone(viewOptions);
      viewOptions.edit = false;
    }

    viewOptions.fields = this.options.fields;
    if (typeof field.parseUpdates === "function") {
      viewOptions.parseUpdates = field.parseUpdates;
    }
    if (!_.isEmpty(field.options)) {
      _.each(field.options, function (val, key) {
        viewOptions[key] = val;
      });
    }

    if (!_.isEmpty(options)) {
      _.each(options, function (val, key) {
        viewOptions[key] = val;
      });
    } else {
      if (viewOptions.hasOwnProperty("onClick")) {
        delete viewOptions.onClick;
      }
    }

    if (field.uid) {
      viewOptions.uid = field.uid;
    }
    if (field.hasOwnProperty("hideInMini")) {
      viewOptions.hideInMini = field.hideInMini;
    }
    if (field.end) {
      viewOptions.end = field.end;
    }
    if (field.link) {
      viewOptions.link = field.link;
    }

    var typeProperty = "type";
    var typeField = getTypeField(this.options.fields);

    if (!_.isEmpty(typeField)) {
      typeProperty = typeField.field;
    }
    viewOptions.typeProperty = typeProperty;
    viewOptions.inCollection = true;
    viewOptions.blueprint = this.blueprint;
    if (viewOptions.edit) {
      viewOptions.commitUpdates = true;
    }

    // Override status field to be able to edit
    if (fieldType === "state") {
      viewOptions.edit = true;
      if (!_.isUndefined(field.edit)) {
        viewOptions.edit = field.edit;
      }
    }

    sb.notify({
      type: "view-field",
      data: {
        type: fieldType,
        property: fieldName,
        obj: obj,
        options: viewOptions,
        ui: ui,
        collId: this.options._id,
      },
    });
  }

  // view functions

  function collection_ui(ui, options, draw) {
    Collections.push({
      _id: options._id,
    });
    var State = {
      type: options.selectedView || "table",
      typeFilter: 0,
    };
    var OptionSets = {
      _default: _.clone(options),
    };

    // data state
    var Blueprint;
    var EntityType;
    var Comps = {};
    var Data = [];
    var View = ui;
    var ViewState = {
      hiddenFields: [],
      edit: false,
      isGrouping: false,
      parent: false,
    };
    var Query = {
      archive: false,
      templates: false,
      filters: [
        /*
{
					field:'propertyName'
					, type:'contains | equals ..'
					, value:'user input'
				}
*/
      ],
      search: {
        isActive: false,
        fields: [],
        search_term: "",
      },
      tagged_with: options.selectedPools,
      where: options.where,
    };

    var Page = {
      page: 1,
      sortCol: "date_created",
      sortDir: "desc",
      sortCast: "date",
    };
    var Pools = [];
    var Types = [];
    var Categories = [];
    var ViewType;
    var fieldsForSubview = {};
    var CurrentSelection = [];
    var OnMobile = $(window).width() <= 768;
    var headerOptions = {
      edit: true,
    };
    var largeModalState = "closed";
    var groupByDiv = {};
    var teamTag;

    // ui methods

    var closeLoader;
    var UpdateSortBtns;
    var updateLastRefreshUi;
    var filterSelections = {};
    var counter = 0;

    // view startup

    function resetOptions(newOptions, optionsKey) {
      // if reloading options set already added to
      // OptionSets list
      if (_.isEmpty(newOptions) && !_.isEmpty(OptionSets[optionsKey])) {
        options = OptionSets[optionsKey];

        // if a new set is provided, use default options as base,
        // and overwrite with new options that do exist
      } else if (!_.isEmpty(newOptions)) {
        _.each(["objectType", "fields", "parseData", "where"], function (key) {
          if (newOptions.hasOwnProperty(key)) {
            options[key] = newOptions[key];
          }
        });
      }

      // set hidden and searchable fields
      _.each(options.fields, function (field, name) {
        if (field.isHidden) {
          ViewState.hiddenFields.push(name);
        }

        if (field.isSearchable) {
          Query.search.fields.push(name);
        }
      });

      if (
        _.isEmpty(Query.search.fields) &&
        options.fields.hasOwnProperty("name")
      ) {
        Query.search.fields = ["name"];
      }

      if (options.sortCol) {
        Page.sortCol = options.sortCol;
      }
      if (options.sortDir) {
        Page.sortDir = options.sortDir;
      }
      if (options.sortCast) {
        Page.sortCast = options.sortCast;
      }
      if (options.groupings) {
        _.each(options.groupings, function (grouping, key) {
          if (key === "by_range" && grouping) {
            options.groupings.by_range = {
              title: "By time",
              group: function (list) {
                // default to ranging over 'date_created' field
                var dateField = "date_created";
                var today = moment().startOf("day");
                var groupDictionary = {
                  today: "Today",
                  tomorrow: "Tomorrow",
                  this_week: "This week",
                  this_month: "This month",
                  later: "Later",
                };

                // check for rangeOver field field
                _.each(options.fields, function (field, key) {
                  if (field.rangeOver) {
                    dateField = key;
                  }
                });

                var groups = _.groupBy(list, function (item) {
                  var itemDate = moment(item[dateField], "YYYY-MM-DD HH:mm:ss");
                  if (today.isSame(itemDate, "day")) {
                    return "today";
                  }
                  if (today.clone().add(1, "day").isSame(itemDate, "day")) {
                    return "tomorrow";
                  }

                  if (today.isSame(itemDate, "week")) {
                    return "this_week";
                  }
                  if (today.isSame(itemDate, "month")) {
                    return "this_month";
                  }

                  return "later";
                });

                var ret = {};
                _.each(groupDictionary, function (groupTitle, groupName) {
                  if (groups[groupName]) {
                    ret[groupName] = {
                      color: "grey",
                      title: groupTitle,
                      value: groupName,
                      data: groups[groupName],
                    };
                  }
                });

                return ret;
              },
            };
          }
        });
      }

      if (!options.hasOwnProperty("create")) {
        options.create = true;
      }

      // add to options set
      if (optionsKey) {
        OptionsSet[optionsKey] = options;
      }

      if (options.data) {
        if (options.data.add) {
          options.data.add(function (newObjs) {
            var arrayMethod = "push";

            if (Page.sortDir === "desc") {
              arrayMethod = "unshift";
            }

            if (_.isObject(newObjs)) {
              if (Array.isArray(Data)) {
                Data[arrayMethod](newObjs);
              } else {
                Data = [newObjs];
              }
            } else if (_.isArray(newObjs)) {
              if (Array.isArray(Data)) {
                _.each(newObjs, function (o) {
                  Data[arrayMethod](o);
                });
              } else {
                Data = [];

                _.each(newObjs, function (o) {
                  Data[arrayMethod](o);
                });
              }
            }

            View.Body.empty();
            View.Body.patch();

            body_ui(View.Body, options, Data);
            View.Body.patch();
          });
        }

        if (options.data.remove) {
          options.data.remove(function (objs) {
            if (_.isObject(objs)) {
              Data = _.reject(Data, function (o) {
                return o.id === objs.id;
              });
            } else if (_.isArray(objs)) {
              _.each(objs, function (obj) {
                Data = _.reject(Data, function (o) {
                  return o.id === obj.id;
                });
              });
            }

            body_ui(View.Body, options, Data);
            View.Body.patch();
          });
        }

        if (options.data.update) {
          options.data.update(function (objs, properties) {
            if (_.isObject(objs)) {
              var oldObj = _.findWhere(Data, { id: objs.id });

              _.each(properties, function (value, key) {
                oldObj[key] = objs[key];
              });
            }

            body_ui(View.Body, options, Data);
            View.Body.patch();
          });
        }
      }

      if ((options.where && options.where.is_template) || options.templates) {
        Query.templates = true;
      }

      // Config subviews from cache
      if (options.config !== undefined) {
        if (options.config.hasOwnProperty("subview")) {
          if (options.config.subview !== "default") {
            options.selectedView = options.config.subview;
          }
        }
      }

      return options;
    }

    function startView(ui, options, draw) {}

    // batch action functions

    function batch_actions_ui(
      options,
      ui,
      selection,
      pools,
      group,
      showLabels,
      config
    ) {
      var currentArgs = arguments;

      var archiveBtnTxt = '<i class="black archive icon"></i> Batch Archive';

      /// for Actions that act on entity types rather than a individual record, pass in entity type
      if (_.isUndefined(selection) && options.hasOwnProperty("entityType")) {
        selection = options.entityType;
      }

      if (Query.archive) {
        archiveBtnTxt = '<i class="yellow redo icon"></i> Restore';
      }

      function groups_ui(right, options) {
        function getGroupDisplayText(group) {
          if (!group) {
            return "&nbsp;Ungrouped";
          }

          if (typeof group === "string") {
            return "&nbsp;Group by " + group;
          } else {
            return "&nbsp;Group by " + group.title;
          }
        }

        if (options.groupings && Object.keys(options.groupings).length > 1) {
          var initialGrp = options.groupings[options.groupBy.field];

          var groupingOptions = _.map(
            options.groupings,
            function (group, fieldName) {
              var grpTxt = getGroupDisplayText(group);
              var isSelected = false;

              return {
                name: grpTxt,
                value: fieldName,
              };
            }
          );
          groupingOptions.unshift({
            name: "&nbsp;Ungrouped",
            value: null,
            selected: true,
          });

          groupByDiv = right.makeNode("groupBy", "div", {
            text:
              '<i class="caret left icon"></i> <span class="text">' +
              getGroupDisplayText(initialGrp) +
              "</span>",
            css: "ui left pointing dropdown item",
            style: "width:100%;",
            listener: {
              onChange: function (value, text) {
                if (value) {
                  if (value === "null") {
                    setGroupBy(options, "");
                  } else {
                    setGroupBy(options, value);
                  }

                  View.Body.empty();
                  body_ui(View.Body, options, Data);
                  View.Body.patch();
                }
              },
              type: "dropdown",
              values: groupingOptions,
              placeholder: getGroupDisplayText(initialGrp),
            },
          });

          right.groupBy.makeNode("menu", "div", {
            css: "left menu",
          });

          return;
        }
      }

      // !TODO: add "(# of filters)" to label
      function filter_ui(ui, shouldPatch) {
        function get_filter_type_options(key, fields, Blueprint) {
          if (Blueprint[key]) {
            switch (Blueprint[key].type) {
              case "string":
                return [
                  {
                    name: "<strong>contains</strong>",
                    value: "contains",
                    selected: true,
                  },
                  {
                    name: "<strong>is</strong>",
                    value: "equal_to",
                    selected: true,
                  },
                ];
                break;

              case "int":
                return [
                  {
                    name: "<strong>=</strong>",
                    value: "equal_to",
                    selected: true,
                  },
                  {
                    name: "<strong><</strong>",
                    value: "less_than",
                    selected: true,
                  },
                  {
                    name: "<strong>></strong>",
                    value: "greater_than",
                    selected: true,
                  },
                ];
                break;
            }
          }

          if (key && fields[key].type === "type") {
            return [
              {
                name: "<strong>=</strong>",
                value: "equal_to",
                selected: true,
              },
            ];
          }

          return [];
        }

        function single_filter_ui(ui, filter, i) {
          function filter_value_ui(ui, filter, shouldPatch) {
            if (filter.field && options.fields[filter.field].type === "type") {
              ui.makeNode("value", "div", {}).makeNode("value", "div", {
                text: '<span class="text"><strong>Types</strong></span>',
                css: "ui dropdown item",
                listener: {
                  onChange: function (i, value) {
                    if (value) {
                      Query.filters[i].value = parseInt(value);
                      refresh_pool(View.Body, options);
                    }
                  }.bind({}, i),
                  type: "dropdown",
                  values: _.map(Types, function (type, i) {
                    return {
                      name: type.name,
                      value: type.id,
                      selected: i === 0,
                    };
                  }),
                  placeholder: "Compare",
                },
              });
            } else {
              ui.makeNode("value", "div", {
                css: "item",
              })
                .makeNode("input", "div", {
                  css: "ui transparent input",
                  text:
                    '<input type="text" value="' +
                    filter.value +
                    '" placeholder="Type...">',
                })
                .listeners.push(
                  function (i, selector) {
                    $(selector).on("change", function () {
                      Query.filters[i].value = $(this).children().first().val();
                      refresh_pool(View.Body, options);
                    });
                  }.bind({}, i)
                );
            }

            if (shouldPatch) {
              ui.value.patch();
            }
          }

          var fieldOptions = [];
          _.each(options.fields, function (field, key) {
            if (
              (Blueprint[key] &&
                (Blueprint[key].type === "string" ||
                  Blueprint[key].type === "int")) ||
              field.type === "type"
            ) {
              var isSelected = filter.field === key;

              fieldOptions.push({
                name:
                  '<strong><i class="' +
                  getFieldTypeIcon(key, options.fields, Blueprint) +
                  ' icon"></i> ' +
                  field.title +
                  "</strong>",
                value: key,
                selected: isSelected,
              });
            }
          });

          // field
          ui.makeNode("field", "div", {
            text: '<span class="text"><strong>Field</strong></span>',
            css: "ui dropdown item",
            listener: {
              onChange: function (i, value) {
                if (value) {
                  Query.filters[i].field = value;

                  $(ui.type.selector).dropdown(
                    "change values",
                    get_filter_type_options(value, options.fields, Blueprint)
                  );

                  filter_value_ui(ui, filter, true);
                }
              }.bind({}, i),
              type: "dropdown",
              values: fieldOptions,
              placeholder: "Field",
            },
          });

          // type
          ui.makeNode("type", "div", {
            text: filter.type,
            css: "item",
          });
          ui.makeNode("type", "div", {
            text: '<span class="text"><strong>Compare</strong></span>',
            css: "ui dropdown item",
            listener: {
              onChange: function (i, value) {
                if (value) {
                  Query.filters[i].type = value;
                  Query.filters[i].value = "";
                  refresh_pool(View.Body, options);
                }
              }.bind({}, i),
              type: "dropdown",
              values: get_filter_type_options(
                filter.field,
                options.fields,
                Blueprint
              ),
              placeholder: "Compare",
            },
          });

          // value
          filter_value_ui(ui, filter);

          // remove filter
          ui.makeNode("rm", "div", {
            text: '<i class="remove icon"></i>',
            css: "ui red icon mini right floated button inverted item",
          }).notify(
            "click",
            {
              type: "collections-run",
              data: {
                run: function (filters, i) {
                  this.empty();
                  this.patch();

                  filters.splice(i, 1);
                  filters_list_ui(this, filters);
                  this.patch();

                  refresh_pool(View.Body, options);
                }.bind(View["large-modal"].body.cont, Query.filters, i),
              },
            },
            sb.moduleId
          );
        }

        function filters_list_ui(ui, filters, cb) {
          _.each(filters, function (filter, i) {
            single_filter_ui(
              ui.makeNode("filter-" + i, "div", {
                css: "ui fluid secondary menu",
                style: "min-width:500px !important;",
              }),
              filter,
              i
            );

            ui.makeNode("br-" + i, "div", {
              css: "ui clearing divider",
              text: "<br />",
            });
          });

          ui.patch();
        }

        ui.makeNode("filt", "div", {
          css: "ui left pointing dropdown item",
          text: '<i class="filter icon"></i> Filter',
          style: "width:100%;",
        });

        ui.filt.notify(
          "click",
          {
            type: "collections-run",
            data: {
              run: function () {
                View["large-modal"].body.empty();
                View["large-modal"].body.patch();
                View["large-modal"].show();

                View["large-modal"].body.makeNode("header", "div", {
                  css: "ui dividing header",
                  text: '<i class="filter icon"></i> Filter',
                });
                View["large-modal"].body.makeNode("cont", "div", { css: "" });

                filters_list_ui(View["large-modal"].body.cont, Query.filters);

                View["large-modal"].body
                  .makeNode("btn", "div", {
                    css: "ui mini right floated green button",
                    text: "Apply",
                  })
                  .notify(
                    "click",
                    {
                      type: "collections-run",
                      data: {
                        run: function () {
                          View["large-modal"].hide();
                          body_ui(View.Body, options, Data);
                          View.Body.patch();
                        },
                      },
                    },
                    sb.moduleId
                  );

                View["large-modal"].body
                  .makeNode("addFilter", "div", {
                    text: '<i class="plus icon"></i>',
                    tag: "button",
                    css: "ui icon mini button",
                  })
                  .notify(
                    "click",
                    {
                      type: "collections-run",
                      data: {
                        run: function (filters) {
                          filters.push({ value: "" });
                          filters_list_ui(this, filters);
                          this.patch();
                        }.bind(View["large-modal"].body.cont, Query.filters),
                      },
                    },
                    sb.moduleId
                  );

                View["large-modal"].body.patch();
              },
            },
          },
          sb.moduleId
        );

        return;
      }

      function editModeToggle_ui(ui) {
        ui.makeNode("c", "div", {
          tag: "a",
          css: "item",
          style: "width:100%;",
        }).makeNode("toggleEdit", "div", {
          css: "ui toggle checkbox",
          text:
            '<input type="checkbox" name="lock">' + "<label>Edit Mode</label>",
          listener: {
            type: "checkbox",
            onChecked: function () {
              ViewState.edit = true;
              body_ui(View.Body, options, Data);
              View.Body.patch();
            },
            onUnchecked: function () {
              ViewState.edit = false;
              body_ui(View.Body, options, Data);
              View.Body.patch();
            },
          },
        });
      }

      function archiveModeToggle_ui(ui) {
        function toggleDeleteUi(archive) {
          if (archive) {
            $(".archive-action-btn").each(function () {
              $(this).html(
                '<i class="ui yellow redo icon"></i> Restore selected'
              );
            });

            $(".archive-action-single-btn").each(function () {
              $(this).html('<i class="ui yellow redo icon"></i> Restore');
            });
          } else {
            $(".archive-action-btn").each(function () {
              $(this).html(
                '<i class="ui black archive icon"></i> Archive selected'
              );
            });

            $(".archive-action-single-btn").each(function () {
              $(this).html('<i class="ui black archive icon"></i> Archive');
            });
          }
        }

        function getItemHtml(archive) {
          if (archive) {
            return '<div data-inverted="" ><i class="history teal icon"></i>View Current</div>';
          } else {
            return '<div data-inverted="" ><i class="history grey icon"></i> View Archive</div>';
          }
        }

        ui.makeNode("toggleArchive", "div", {
          tag: "a",
          text: getItemHtml(Query.archive),
          css: "item",
          style: "width:100%;",
        }).notify("click", {
          type: "collections-run",
          data: {
            run: function () {
              Query.archive = !Query.archive;
              $(this.selector).html(getItemHtml(Query.archive));

              if (typeof showLabels === "function") {
                showLabels(Query);
              }

              toggleDeleteUi(Query.archive);
              refresh_pool(View.Body, options);
            }.bind(ui.toggleArchive),
          },
        });
      }

      function visible_cols_ui(ui, Query, pools) {
        function getSortBtnCss(key, dir) {
          if (Page.sortCol === key && Page.sortDir === dir) {
            return "teal link icon active item";
          }

          return "teal link icon item";
        }

        function toggleSortSelection(key, dir) {
          toggleSort(key, dir);
        }

        ui.makeNode("visCol", "div", {
          css: "ui left pointing dropdown item",
          text: '<i class="columns icon"></i> Columns',
          style: "width:100%;",
        });

        ui.visCol.makeNode("menu", "div", { css: "left menu" });
        ui.visCol.menu.makeNode("cont", "div", { style: "padding:20px;" });

        var i = 0;

        // Checking collections cache
        if (options.hasOwnProperty("config")) {
          if (options.config) {
            if (options.config.hasOwnProperty("fields")) {
              if (!_.isEmpty(options.config.fields)) {
                _.each(options.config.fields, function (field, key) {
                  ViewState.hiddenFields.push(field);
                });
              }
            }
          }
        }

        _.each(options.fields, function (field, key) {
          // place divider
          if (i > 0) {
            ui.visCol.menu.cont.makeNode("br-" + key, "div", {
              css: "ui clearing divider",
              text: "<br />",
            });
          }

          // toggle field visibility
          var selectedText = ' checked=""';
          if (_.contains(ViewState.hiddenFields, key)) {
            selectedText = "";
          }

          var icon = getFieldTypeIcon(key, options.fields, Blueprint);

          ui.visCol.menu.cont.makeNode("field-" + key, "div", {
            text:
              '<input type="checkbox"' +
              selectedText +
              '><label><i class="text-muted ' +
              icon +
              ' icon"></i>' +
              field.title +
              "</label>",
            css: "ui slider checkbox",
            listener: {
              type: "checkbox",
              onChecked: function (fieldName) {
                options.config.fields = _.filter(
                  options.config.fields,
                  function (v) {
                    return v !== fieldName;
                  }
                );

                // !CACHING - caching fields per subview
                update_browserCache(options, function (data, callback) {
                  data.fields = options.config.fields;

                  callback(data);
                });

                ViewState.hiddenFields = _.reject(
                  ViewState.hiddenFields,
                  function (hiddenField) {
                    return hiddenField === fieldName;
                  }
                );
                body_ui(View.Body, options, Data);
                View.Body.patch();
              }.bind({}, key),
              onUnchecked: function (fieldName) {
                options.config.fields.push(fieldName);

                // !CACHING - caching fields per subview
                update_browserCache(options, function (data, callback) {
                  data.fields = options.config.fields;

                  callback(data);
                });

                ViewState.hiddenFields.push(fieldName);
                body_ui(View.Body, options, Data);
                View.Body.patch();
              }.bind({}, key),
            },
          });

          // if field corresponds to a blueprint property definition,
          // allow for some query management on that field

          if (Blueprint[key]) {
            // sort btns
            var sortCss = getSortBtnCss(key, "asc");

            ui.visCol.menu.cont.makeNode("sort-btns-" + key, "div", {
              css: "ui right floated text tiny menu",
            });

            ui.visCol.menu.cont["sort-btns-" + key]
              .makeNode("asc", "div", {
                css: sortCss,
                text: '<i class="arrow up icon"></i>',
              })
              .notify(
                "click",
                {
                  type: "collections-run",
                  data: {
                    run: toggleSortSelection.bind({}, key, "asc"),
                  },
                },
                sb.moduleId
              );

            sortCss = getSortBtnCss(key, "desc");

            ui.visCol.menu.cont["sort-btns-" + key]
              .makeNode("desc", "div", {
                css: sortCss,
                text: '<i class="arrow down icon"></i>',
              })
              .notify(
                "click",
                {
                  type: "collections-run",
                  data: {
                    run: toggleSortSelection.bind({}, key, "desc"),
                  },
                },
                sb.moduleId
              );
          }

          i++;
        });

        UpdateSortBtns = function (key, dir, oldKey, oldDir) {
          // remove "active" class from old selection
          if (this.visCol.menu.cont["sort-btns-" + oldKey]) {
            this.visCol.menu.cont["sort-btns-" + oldKey][oldDir].removeClass(
              "active"
            );
          }

          // add "active" class to new selection
          this.visCol.menu.cont["sort-btns-" + key][dir].addClass("active");
        }.bind(ui);
      }

      function viewTemplatesToggle_ui(ui) {
        var checked = options.templates ? 'checked="checked"' : "";

        ui.makeNode("templates", "div", {
          tag: "a",
          css: "item",
          style: "width:100%;",
        }).makeNode("toggleTemplates", "div", {
          css: "ui toggle checkbox",
          text:
            '<input type="checkbox" name="template"' +
            checked +
            ">" +
            "<label>View Templates</label>",
          listener: {
            type: "checkbox",
            onChecked: function () {
              Query.templates = !Query.templates;
              options.templates = true;

              if (typeof showLabels === "function") {
                showLabels(Query);
              }

              batch_actions_ui.apply(null, currentArgs);

              refresh_pool(View.Body, options);
            },
            onUnchecked: function () {
              Query.templates = !Query.templates;
              options.templates = false;

              if (typeof showLabels === "function") {
                showLabels(Query);
              }

              batch_actions_ui.apply(null, currentArgs);

              refresh_pool(View.Body, options);
            },
          },
        });
      }

      var menuCss = "menu";
      var dropdownCss =
        "ui right floated stackable circular icon dropdown basic white button";
      var dropdownStyle = "border:0px;box-shadow:none;";
      if (config) {
        if (config.dropdownCss) {
          dropdownCss = config.dropdownCss;
        }
        if (config.openDir) {
          menuCss += " " + config.openDir;
        }
      }

      if (options.actions && options.actions.create && !config.hideNewBtn) {
        ui.makeNode("c", "div", {
          css: "ui item",
          style: "padding: 0em !important;",
        });
        ui.c.makeNode("btns", "div", {
          css: "ui mini buttons",
          style: "padding:0.5em !important; min-height:28px; max-height:28px;",
        });
        ui.c.btns.makeNode("new", "div", {
          css: "ui green button",
          text: '<i class="plus icon" style="margin-right:0 !important;"></i> New',
          style: "padding:0.8em !important",
        });

        switch (typeof options.actions.create) {
          case "function":
            ui.c.btns.new.notify(
              "click",
              {
                type: "collections-run",
                data: {
                  run: function () {
                    var seed = getNewSeed();

                    View["large-modal"].empty();
                    View["large-modal"].patch();

                      if (
                          options.objectType == "contracts" &&
                          options.state.pageObject.group_type == "Project"
                      ) {
                        if (
                            options.state.pageObject.hasOwnProperty("main_contact")
                        ) {
                          if (!options.state.pageObject.main_contact) {
                            View["large-modal"].show();
                          }
                        }
                      } else if (options.objectType != "contracts") {
                        View["large-modal"].show();
                      }

                      options.actions.create(
                          View["large-modal"],
                          seed,
                          function (response, keepOpen) {
                            if (!keepOpen) {
                          View["large-modal"].hide();
                            }

                            setTimeout(function () {
                              if (Array.isArray(Data)) {
                                Data.unshift(response);
                              } else {
                                Data = [response];
                              }

                              body_ui(View.Body, options, Data);
                              View.Body.patch();
                            }, 1);
                          },
                          Blueprint,
                          {
                            setTemplateProps: templateForm_ui,
                            options: options,
                          },
                          function (refresh) {
                            if (refresh) {
                              refresh_pool(View.Body, options);
                            }
                          }
                      );
                  },
                },
              },
              sb.moduleId
            );
            break;

          case "object":
            if (options.actions.create.type === "function") {
              ui.c.btns.new.notify("click", {
                type: "collections-run",
                data: {
                  run: function () {
                    options.actions.create.action();
                  },
                },
              });
            } else if (options.actions.create.type === "quick") {
              ui.c.btns.new.notify("click", {
                type: "collections-run",
                data: {
                  run: function () {
                    var seed = getNewSeed(seed);

                    options.actions.create.action(seed, function (response) {
                      if (Array.isArray(Data)) {
                        Data.push(response);
                      } else {
                        Data = [response];
                      }

                      body_ui(View.Body, options, Data);
                      View.Body.patch();
                    });
                  },
                },
              });
            } else if (options.actions.create.type === "popup") {
              ui.c.btns.new.listeners.push(function (selector) {
                $(selector).popup({
                  on: "click",
                  inline: true,
                  forcePosition: true,
                  position: "bottom right",
                  onShow: function () {
                    options.actions.create.action(ui.popup, getNewSeed());
                  },
                });
              });
            }
            break;
        }

        if (options.actions.createFromTemplate) {
          var tempDomId =
            "tmplate-" +
            sb.dom.randomString(
              6,
              "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
            );

          ui.c.btns.makeNode("temp", "div", {
            css: "ui floating dropdown icon green button",
            text: '<i class="far fa-clone"></i>',
            style: "padding:0.5em !important; border-left:1px solid #16BF4E;",
            id: tempDomId,
          });

          ui.c.btns.temp.notify("click", {
            type: "collections-run",
            data: {
              run: function () {
                sb.notify({
                  type: "get-sys-floater",
                  data: {
                    element: "#" + tempDomId,
                    minWidth: "300px",
                    maxWidth: "300px",
                    offsetTop: 35,
                    padding: "0px",
                    callback: function (floater) {
                      var seed = getNewSeed();

                      options.actions.createFromTemplate(
                        floater,
                        seed,
                        function (response, keepOpen) {
                          setTimeout(function () {
                            if (Array.isArray(Data)) {
                              Data.unshift(response);
                            } else {
                              Data = [response];
                            }

                            body_ui(View.Body, options, Data);
                            View.Body.patch();
                          }, 1);
                        },
                        Blueprint,
                        {
                          setTemplateProps: templateForm_ui,
                          options: options,
                          View: View["large-modal"],
                        }
                      );

                      floater.container.patch();
                    },
                  },
                });
              },
            },
          });
        }
      }

      if (!options) {
        var options = {};
      }

      if (!options.hasOwnProperty("menu") || !options.menu) {
        options.menu = {
          actions: true,
        };
      }

      if (options.menu.actions !== false) {
        ui.makeNode("actions", "div", {
          css: dropdownCss,
          text: '<i class="ellipsis horizontal icon" style="font-size:1.5em !important;"></i>',
          listener: {
            type: "dropdown",
            action: "hide",
          },
          style: "border:0px; box-shadow:none; padding:5px !important;",
        }).makeNode("menu", "div", {
          css: menuCss,
          style: "",
        });

        if (options.actions) {
          _.each(options.actions, function (action) {
            if (action.batchAction === false) {
              return;
            }
            if (
              action.name &&
              action.name != "create" &&
              action.name != "createFromTemplate" &&
              !action.headerAction
            ) {
              if (action.href) {
                ui.actions.menu.makeNode(action.id, "div", {
                  text:
                    '<i class="' +
                    action.icon +
                    " " +
                    action.color +
                    ' icon"></i> ' +
                    action.name,
                  css: "ui mini " + action.color + " item inverted",
                  tag: "a",
                  href: action.href,
                  target: "_blank",
                  style: "width:100%;",
                });
              } else {
                ui.actions.menu.makeNode(action.id, "div", {
                  text:
                    '<i class="' + action.icon + ' icon"></i> ' + action.name,
                  css: "ui mini " + action.color + " item inverted button",
                  tag: "a",
                  style: "width:100%;",
                });

                ui.actions.menu[action.id].notify(
                  "click",
                  {
                    type: "collections-run",
                    data: {
                      run: function (selection) {
                        var seed = getNewSeed();

                        View["large-modal"].body.empty();
                        View["large-modal"].body.patch();

                        View["large-modal"].show();

                        action.view(
                          View["large-modal"].body,
                          seed,
                          // !TODO: refactor this to a refresh_ui func
                          function (response) {
                            View["large-modal"].hide();

                            if (Array.isArray(Data)) {
                              Data.push(response);
                            } else {
                              Data = [response];
                            }

                            body_ui(View.Body, options, Data);
                            View.Body.patch();
                          },
                          CurrentSelection
                        );
                      }.bind({}, selection),
                    },
                  },
                  sb.moduleId
                );
              }
            }
          });
        }

        ui.actions.menu.makeNode("d1", "div", {
          css: "divider",
          style: "margin:0;",
        });

        if (headerOptions.edit) editModeToggle_ui(ui.actions.menu);

        if (options.templates === true || options.templates === undefined) {
          viewTemplatesToggle_ui(ui.actions.menu);
        }

        archiveModeToggle_ui(ui.actions.menu);

        if (options.actions.archive !== false) {
          ui.actions.menu
            .makeNode("archive", "div", {
              text: archiveBtnTxt,
              css: "item archive-action-btn",
              style: "width:100%;",
            })
            .notify(
              "click",
              {
                type: "collections-run",
                data: {
                  run: function (selection) {
                    archive_selected_items(selection, options, function () {});
                  }.bind({}, selection),
                },
              },
              sb.moduleId
            );
        }

        if (options.actions.batchTags !== false) {
          ui.actions.menu
            .makeNode("tag", "div", {
              css: "item",
              text: '<i class="teal tag icon"></i> Batch Tag',
              style: "width:100%;",
            })
            .notify(
              "click",
              {
                type: "collections-run",
                data: {
                  run: function (selection) {
                    tag_selected_items(undefined, selection);
                  }.bind({}, selection),
                },
              },
              sb.moduleId
            );
        }

        ui.actions.menu.makeNode("d2", "div", {
          css: "divider",
          style: "margin:0;",
        });

        if (options.groupings && Object.keys(options.groupings).length > 1)
          groups_ui(ui.actions.menu, options);

        if (options.actions.filter !== false) {
          filter_ui(ui.actions.menu);
        }

        if (options.actions.columns !== false) {
          visible_cols_ui(ui.actions.menu, Query, pools);
        }

        ui.actions.menu.makeNode("d3", "div", {
          css: "divider",
          style: "margin:0;",
        });

        if (options.actions.downloadCSV) {

          var sortFieldOptions = [];

          _.map(options.fields, function(item){
            sortFieldOptions.push({
              value: item.title,
              name: item.title
            });
          });

          var sortOrderOptions = [
            {
              value: 'asc',
              name: 'Asc'
            },
            {
              value: 'desc',
              name: 'Desc',
              selected: true
            }
          ];

          function createModalChoice(_callback) {
            sb.notify({
              type: 'get-sys-modal'
              , data: {
                callback: function (modal) {

                  modal.body.makeNode("header", "headerText", {
                    text: "Sort Results by",
                  });


                  modal.body.makeNode("headerBreak", "lineBreak", {spaces: 1});

                  var formSetup = {
                    sortBy: {
                      type: "select",
                      name: "sortBy",
                      label: "Sort By",
                      options: sortFieldOptions,
                    },
                    sortOrder: {
                      type: "select",
                      name: "sortOrder",
                      label: "Sort Order",
                      options: sortOrderOptions,
                    },
                  };

                  modal.body.makeNode("form", "form", formSetup);
                  modal.footer.makeNode("btnsBreak", "lineBreak", {spaces: 1});
                  modal.footer.makeNode("btns", "buttonGroup", {css: ""});
                  modal.footer.btns
                      .makeNode("button", "button", {
                        text: 'Export <i class="fa fa-excel-o"></i>',
                        css: "pda-btn-green",
                      })
                      .notify('click', {
                        type: 'contracts-run',
                        data: {
                          run: function () {
                            modal.hide();
                            _callback(modal.body.form.process().fields);
                          }
                        }
                      }, sb.moduleId);

                  modal.body.patch();
                  modal.footer.patch();

                  modal.show();

                }
              }
            });
          }
          options.actions.downloadCSV = {
            id: "downloadCSV",
            icon: "download",
            color: "red",
            style: "width:100%;",
            title: "Download to CSV",
            headerAction: true,
            singleAction: false,
            action: function () {
              if(options.objectType == 'groups') {
                createModalChoice(
                    function (form) {

                      options.sortOptions = {
                        sortBy: form.sortBy.value,
                        sortOrder: form.sortOrder.value
                      };

                      download_csv({
                        options: options,
                        Query: Query,
                        Types: Types,
                        ViewType: ViewType,
                      });
                    });
              } else {
                      download_csv({
                        options: options,
                        Query: Query,
                        Types: Types,
                        ViewType: ViewType,
                      });
              }
            },
            requireSelection: false,
            ui: false,
          };
        }

        if (options.actions.downloadTSV) {
          options.actions.downloadTSV = {
            id: "downloadTSV",
            icon: "download",
            color: "red",
            style: "width:100%;",
            title: "Download to TSV",
            headerAction: true,
            singleAction: false,
            action: function () {
              download_tsv({
                options: options,
                Query: Query,
                Types: Types,
                ViewType: ViewType,
              });
            },
            requireSelection: false,
            ui: false,
          };
        }

        if (options.actions.downloadPDF) {
          options.actions.downloadPDF = {
            id: "downloadPDF",
            icon: "file pdf outline",
            color: "red",
            style: "width:100%;",
            title: "Download PDF",
            ui: false,
            headerAction: true,
            action: function () {
              download_pdf({
                options: options,
                Query: Query,
                Types: Types,
                ViewType: ViewType,
              });
            },
          };
        }

        if (options.actions.download !== false) {
          ui.actions.menu
            .makeNode("downloadAction", "div", {
              css: "ui basic fluid item download-action-single-btn",
              text: '<i class="cloud download alternate icon"></i> Download',
              style: "width:100%;",
              headerAction: true,
              requireSelection: true,
              ui: false,
              modal: true,
            })
            .notify(
              "click",
              {
                type: "collections-run",
                data: {
                  run: function () {
                    // Send to download
                    sb.data.db.controller(
                      "downloadFilesAsZip",
                      {
                        ids: CurrentSelection,
                      },
                      function (data) {
                        // Hide the alert
                        $(".toast").click();

                        if (data.url) {
                          // Prompt download of file
                          window.location.href = data.url;
                        } else {
                          // Tell the user there was an error
                          sb.dom.alerts.alert(
                            "Error!",
                            "There was an issue zipping up your files. Please try again.",
                            "error"
                          );
                        }
                      },
                      "https://bento.infinityhospitality.net/api/_get.php?api_webform=true&pagodaAPIKey=" +
                        appConfig.instance +
                        "&do="
                    );

                    // Show alert
                    sb.notify({
                      type: "display-alert",
                      data: {
                        header: "Preparing to Download",
                        body: "Please stay on this screen while your files are being prepared to download.",
                        color: "green",
                        displayTime: 0,
                      },
                    });
                  },
                },
              },
              sb.moduleId
            );
        }

        _.each(options.actions, function (actionSetting, name) {
          // only show actions set flagged as 'headerActions' = true
          if (!actionSetting.headerAction) {
            return;
          }
          ui.actions.menu
            .makeNode(name, "div", {
              text:
                '<i class="' +
                actionSetting.color +
                " " +
                actionSetting.icon +
                ' icon"></i> ' +
                actionSetting.title,
              css: "item",
              style: "width:100%;",
            })
            .notify(
              "click",
              {
                type: "collections-run",
                data: {
                  run: function (selection) {
                    if (
                      //State.type == 'table'
                      _.isEmpty(CurrentSelection) &&
                      actionSetting.requireSelection !== false &&
                      actionSetting.hasOwnProperty("requireSelection")
                    ) {
                      sb.dom.alerts.alert(
                        "No items selected",
                        "Select some items first.",
                        "warning"
                      );
                      return false;
                    }

                    if (typeof actionSetting.headerAction === "function") {
                      if (
                        actionSetting.hasOwnProperty("ui") &&
                        actionSetting.ui !== false
                      ) {
                        var customView = {};

                        customView = View["large-modal"].body.makeNode(
                          "c",
                          "div",
                          { style: "min-height:37vh!important;" }
                        );

                        View["large-modal"].body.patch();
                        View["large-modal"].show();

                        actionSetting.headerAction(
                          CurrentSelection,
                          customView,
                          function (shouldUpdateList, setup) {
                            View["large-modal"].hide();

                            if (shouldUpdateList) {
                              refresh_pool(View.Body, options);
                            }
                          },
                          options
                        );
                      } else {
                        if (_.isEmpty(CurrentSelection) && selection) {
                          CurrentSelection = selection;
                        }

                        actionSetting.headerAction(
                          CurrentSelection,
                          options.state,
                          function (shouldUpdateList) {
                            if (shouldUpdateList) {
                              refresh_pool(View.Body, options);
                              CurrentSelection = []; // reset selections list
                            }
                          },
                          Query
                        );
                      }
                    } else {
                      var customView = {};

                      if (
                        actionSetting.hasOwnProperty("action") &&
                        typeof actionSetting.action == "function"
                      ) {
                        if (actionSetting.ui !== false) {
                          customView = View["large-modal"].body.makeNode(
                            "c",
                            "div",
                            { style: "min-height:37vh!important;" }
                          );

                          View["large-modal"].body.patch();
                          View["large-modal"].show();
                        }

                        actionSetting.action(
                          _.filter(Data, function (d) {
                            var currentSelection = [];
                            _.each(CurrentSelection, function (selection) {
                              currentSelection.push(parseInt(selection));
                            });
                            if (currentSelection.length > 0) {
                              if (
                                _.contains(currentSelection, d.id) ||
                                parseInt(currentSelection) === d.id
                              ) {
                                return d;
                              }
                            } else {
                              return d;
                            }
                          }),
                          customView,
                          function (shouldUpdateList) {
                            View["large-modal"].hide();

                            if (shouldUpdateList) {
                              refresh_pool(View.Body, options);
                            }
                          },
                          options
                        );
                      }
                    }
                  }.bind({}, selection),
                },
              },
              sb.moduleId
            );
        });
      }
    }

    function archive_selected_items(itemId, options, onComplete) {
      var hasOverrideAction = false;
      var itemsToArchive = _.map(CurrentSelection, function (item) {
        return parseInt(item);
      });

      if (_.isEmpty(CurrentSelection) && itemId === undefined) {
        sb.dom.alerts.alert(
          "No items selected",
          "Select some items first.",
          "warning"
        );
        return false;
      }

      var msgTxt = "Send to archive?";
      var txt = "You can always un-archive this later.";
      var updateFunc = "erase";
      if (Query.archive) {
        updateFunc = "restore";
        msgTxt = "Restore from archive?";
        txt = "This will return this back where it came from.";
      }

      sb.dom.alerts.ask(
        {
          title: msgTxt,
          text: txt,
        },
        function (response) {
          swal.disableButtons();

          if (response) {
            if (itemId !== undefined && CurrentSelection.length < 1)
              itemsToArchive = itemId;

            if (options) {
              if (options.hasOwnProperty("actions")) {
                if (options.actions) {
                  if (options.actions.hasOwnProperty("archive")) {
                    if (options.actions.archive) {
                      if (options.actions.archive.hasOwnProperty("action")) {
                        if (
                          typeof options.actions.archive.action === "function"
                        ) {
                          hasOverrideAction = true;

                          options.actions.archive.action(
                            itemsToArchive,
                            function (response) {
                              swal.close();

                              if (response) {
                                CurrentSelection = [];
                                refresh_pool(View.Body, options);

                                if (typeof onComplete === "function") {
                                  onComplete();
                                }
                              } else if (typeof onComplete === "function") {
                                onComplete(false);
                              }
                            }
                          );
                        }
                      }
                    }
                  }
                }
              }
            }

            if (!hasOverrideAction) {
              sb.data.db.obj[updateFunc](
                options.objectType,
                itemsToArchive,
                function (response) {
                  swal.close();

                  if (response) {
                    CurrentSelection = [];
                    refresh_pool(View.Body, options);

                    if (typeof onComplete === "function") {
                      onComplete();
                    }
                  } else if (typeof onComplete === "function") {
                    onComplete(false);
                  }
                }
              );
            }
          }
        }
      );
    }

    function download_csv(setup) {
      var dataToConvert = [];
      var headerRow = [];
      var fields = options.fields;
      var metricRow = [];
      var pageObject = {
        page: 1,
        sortCol: "date_created",
        sortDir: "desc",
        sortCast: "date",
        pageLength: 50,
      };

      var csvSorteable =
        options.hasOwnProperty("sortCsv") && options.sortCsv === true;
      if (csvSorteable) {
        pageObject.sortCol = options.sortCol;
        pageObject.sortDir = options.sortDir;
        if (options.hasOwnProperty("sortCast")) {
          pageObject.sortCast = options.sortCast;
        }
      }
      var fieldsClone = _.clone(options.fields);
      if (!setup.page) {
        setup.page = pageObject;
    }
      setup.Query.where.childObjs.tagged_with = {
        name: true,
        fname: true,
        lname: true,
      };

      fields = _.reject(setup.options.fields, function (field, key) {
        field.name = key;
        return field.isHidden == true;
      });

      // Hide fields without titles
      fields = _.reject(fields, function (field) {
        return _.isEmpty(field.title);
      });

      // Add tags

      if (setup) {
        if (setup.hasOwnProperty("options")) {
          if (setup.options) {
            if (setup.options.hasOwnProperty("objectType")) {
              if (setup.options.objectType) {
                if (setup.options.objectType === "time_entries") {
                  fields.push({
                    title: "Tags",

                    type: "tags",

                    name: "tagged_with",
                  });
                }
              }
            }
          }
        }
      }

      function getWorkflows(blueprint, onComplete) {
        var workflowIds = [];
        _.each(blueprint, function (property, key) {
          if (
            property.fieldType === "state" &&
            parseInt(property.workflow) &&
            !property.is_archived
          ) {
            workflowIds.push(parseInt(property.workflow));
          }
        });

        sb.data.db.obj.getById(
          "entity_workflow",
          workflowIds,
          function (workflows) {
            _.each(blueprint, function (property, key) {
              if (property.fieldType === "state" && !property.is_archived) {
                var wf = _.findWhere(workflows, {
                  id: parseInt(property.workflow),
                });

                if (!_.isEmpty(wf)) {
                  property.workflow = wf;
                }
              }
            });

            onComplete(blueprint);
          }
        );
      }

      function build_csv_row(list, memo) {
        ///construct header row for csv on first pass
        if (_.isEmpty(headerRow)) {
          _.each(fields, function (val, key) {
            if (!_.isUndefined(fields[key])) {
              if (val.title) {
                headerRow.push('"' + val.title + '"');
              } else {
                headerRow.push('"' + key + '"');
              }

              metricRow.push(key);
            }
          });

          dataToConvert.push(headerRow);
        }

        ///construct item row data on every pass with paged data
        if (!_.isUndefined(list) && !_.isEmpty(list) ) {
          _.map(list, function (inv) {
            var singleRow = [];

            _.each(fields, function (val, key) {
              var txt = "";

              ///use view function to format data else return value
              if (typeof fields[key].view === "function") {
                txt =
                  fields[key].view(null, inv) !== undefined
                    ? fields[key].view(null, inv)
                    : "";

                return singleRow.push('"' + txt + '"');
              } else {
                var retVal = inv[fields[key].name];

                // If field type is a date, additional formatting
                if (val.hasOwnProperty("type")) {
                  switch (val.type) {
                    case "date":
                      retVal = moment(inv[fields[key].name]).format(
                        "MM/DD/YYYY"
                      );
                      break;

                    case "priority":
                      switch (inv[fields[key].name]) {
                        case 0:
                          retVal = "Lowest";
                          break;

                        case 1:
                          retVal = "Low";
                          break;

                        case 2:
                          retVal = "Medium";
                          break;

                        case 3:
                          retVal = "High";
                          break;

                        case 4:
                          retVal = "Highest";
                          break;
                      }

                      break;

                    case "users":
                    case "user":
                      if (_.isEmpty(retVal)) {
                        retVal = "";
                      } else {
                        if (Array.isArray(retVal)) {
                          var tmpNames = [];
                          retVal = "";
                          _.each(inv[fields[key].name], function (usr) {
                            tmpNames.push(usr.fname + " " + usr.lname);
                          });
                          retVal = tmpNames.join(", ");
                        } else if (retVal.name) {
                          retVal = retVal.name;
                        }
                      }
                      break;

                    case "locations":
                    case "location":
                    case "tags":
                      if (Array.isArray(retVal)) {
                        var tmpNames = [];

                        retVal = "";

                        _.each(inv[fields[key].name], function (loc) {
                          if (loc) {
                            if (loc.hasOwnProperty("name")) {
                              if (loc.name) {
                                tmpNames.push(loc.name);
                              }
                            }
                          }
                        });

                        retVal = tmpNames.join(", ");
                      } else if (retVal.name) {
                        retVal = retVal.name;
                      }

                      break;

                    case "state":
                      if (inv.type) {
                        if (inv.type.states) {
                          if (_.findWhere(inv.type.states, { uid: inv.state })) {
                            retVal = _.findWhere(inv.type.states, {
                              uid: inv.state,
                            }).name;
                          } else {
                            retVal = _.findWhere(inv.type.states, {
                              isEntryPoint: 1,
                            }).name;
                          }
                        }
                      }

                      break;
                  }
                }

                if (_.isEmpty(retVal)) {
                  retVal = "";
                }
                return singleRow.push('"' + retVal + '"');
              }
            });

            dataToConvert.push(singleRow);
          });
        }

        if (options.metrics) {
          ///sum data calculations
          _.each(options.metrics, function (opt, item) {
            /*
						///grab metric row title
						if(metricRow.indexOf(opt.title) < 0)
							metricRow.push(opt.title);
*/

            if (opt.fields) {
              ///construct memo object to keep running total of metrics
              _.mapObject(opt.fields, function (v, k) {
                if (typeof v === "function") {
                  ///call metric view function to format data
                  if (!memo[k]) {
                    memo[k] = v(null, list, true, list);
                  } else {
                    memo[k] = memo[k] + v(null, list, true, list);
                  }
                } else if (
                  opt &&
                  opt.fields &&
                  typeof opt.fields[k] &&
                  typeof opt.fields[k].view === "function"
                ) {
                  memo[k] = opt.fields[k].view(null, list, true, list);
                }
              });
            }
          });
        }

        return;
      }

      function build_csv_doc(memo) {

        ///intial check against fields on table
        _.each(fieldsClone, function (val, key) {
          ///metricRow has index values from HeaderRow (to be used as reference)
          var metricRowPosition = _.findIndex(headerRow, function (item) {
            return item == '"' + val.title + '"';
          });

          ///determine if fieldsClone column needs to be mapped
          if (memo.hasOwnProperty(key)) {
            ///map memo obj to get parse view functions
            _.mapObject(memo, function (v, k) {
              ///find index in relation to header row columns
              var index = _.findIndex(headerRow, function (rowCol) {
                return rowCol == '"' + k + '"';
              });

              // If not found, check against field
              if (index == -1) {
                if (fieldsClone && fieldsClone[k]) {
                  index = _.findIndex(headerRow, function (rowCol) {
                    return rowCol == '"' + fieldsClone[k].title + '"';
                  });
                }
              }

              if (index > 0) {
                ///use metrics view fn to format data
                if (v !== null && v != undefined) {
                  metricRow.splice(index, 1, '"' + v + '"');
                } else if (v) {
                  if (options.metrics.sum.fields[k]) {
                    if (typeof options.metrics.sum.fields[k] === "function") {
                      metricRow.splice(
                        index,
                        1,
                        '"' +
                          options.metrics.sum.fields[k](null, v, false) +
                          '"'
                      );
                    } else if (
                      typeof options.metrics.sum.fields[k].view === "function"
                    ) {
                      metricRow.splice(
                        index,
                        1,
                        '"' +
                          options.metrics.sum.fields[k].view(null, v, false) +
                          '"'
                      );
                    }
                  }
                }
              }
            });
          } else {
            ///replace reference item in MetricRow array with Empty Val

            metricRow.splice(metricRowPosition, 1, " ");
          }
        });

        ///construct metric row data for csv document
        /*
				_.mapObject(memo, function(v, k){

					///find index in relation to header row columns
					var index = _.findIndex(headerRow, function(rowCol){

						return rowCol == '"'+ k +'"';
					});

					// If not found, check against field
					if (index == -1) {

						if (
							fieldsClone
							&& fieldsClone[k]
						) {

							index = _.findIndex(headerRow, function(rowCol){

								return rowCol == '"'+ fieldsClone[k].title +'"';

							});

						}

					}

					if(index > 0){
						///use metrics view fn to format data

						if(v){
							metricRow.splice(index, 0, '"'+ v + '"')
						}else{

							if (options.metrics.sum.fields[k]) {
								if (typeof options.metrics.sum.fields[k] === 'function') {
									metricRow.splice(index, 0, '"'+ options.metrics.sum.fields[k](null, v, false) + '"');
								} else if (typeof options.metrics.sum.fields[k].view === 'function') {
									metricRow.splice(index, 0, '"'+ options.metrics.sum.fields[k].view(null, v, false) + '"');
								}
							}
						}

					} else {

						metricRow.push(' ,');
					}

				});
*/

        ///add metricRow to data to convert collection
        dataToConvert.push(metricRow);

        var csvContent = "data:text/csv;charset=utf-8,";
        var sortedArray = false;

        //setting for exported order
        var sortOptions = setup.options && setup.options.sortOptions;
        if(sortOptions) {
          var orderAsc = sortOptions.sortOrder == 'asc' ? true : false;
          var orderBy = sortOptions.sortBy;
          var orderByNumber = false;
          var orderByFormat = '"' + orderBy + '"';

          var isDate = ['Date Intake Complete', 'Due Date', 'Fiscal Year End Date', 'Date of Last Activity', 'Start Date', 'End Date'].includes(orderBy);

          const headerArray = dataToConvert.shift();

          _.forEach(headerArray, function (item, index) {
            if (item.toUpperCase() == orderByFormat.toUpperCase()) {
              orderByNumber = index;
            }
          });

          var sortedArray = dataToConvert.sort(function (a, b) {
            if (orderByNumber) {

              var c_a = isDate? moment(a[orderByNumber], 'MM/DD/YY').toDate() : a[orderByNumber].toUpperCase();
              var c_b = isDate? moment(b[orderByNumber], 'MM/DD/YY').toDate() : b[orderByNumber].toUpperCase();
              var isAsc = orderAsc ? 1 : -1;

              if (c_a == c_b) {
                return 0;
              }
              if (c_a < c_b) {
                return isAsc * -1;
              }
              if (c_a > c_b) {
                return isAsc * 1;
              }
            } else {
              return 0;
            }

          });
          sortedArray = [headerArray].concat(sortedArray);
        }

        var rowsInfo = sortedArray ? sortedArray : dataToConvert;

        rowsInfo.forEach(function (infoArray, index) {
          if (infoArray == headerRow) {
            infoArray = _.map(infoArray, function (header) {
              header = header.replace(/['"]+/g, "");
              header = header.replace(/_/g, " ");

              return (header = header[0].toUpperCase() + header.slice(1));
            });
          } else {
            if(infoArray[2] && infoArray[2].startsWith('" at ')){
              infoArray[2] = infoArray[2].replace('" at ', '"');
            }
          }

          dataString = infoArray.join(",");

          csvContent += dataString.replace(/#/g, "") + "\n";
        });

        // Hide loader
        $("#loader").fadeOut();

        var encodedUri = encodeURI(csvContent);
        var link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "download.csv");
        document.body.appendChild(link); // Required for FF

        link.click();

        return;
      }

      // Show loader
      $("#loader").fadeIn();

      pageThroughData(setup, build_csv_row, build_csv_doc);
    }

    function download_tsv(setup) {
      var dataToConvert = [];
      var headerRow = [];
      var fields = options.fields;
      var metricRow = [];
      var pageObject = {
        page: 1,
        sortCol: "date_created",
        sortDir: "desc",
        sortCast: "date",
        pageLength: 50,
      };
      var fieldsClone = _.clone(options.fields);
      setup.page = pageObject;

      fields = _.reject(setup.options.fields, function (field) {
        return field.isHidden == true;
      });

      function build_csv_row(list, memo) {
        ///construct header row for csv on first pass
        if (_.isEmpty(headerRow)) {
          _.each(fields, function (val, key) {
            if (!_.isUndefined(fields[key])) {
              if (val.title) {
                headerRow.push('"' + val.title + '"');
              } else {
                headerRow.push('"' + key + '"');
              }
            }
          });

          dataToConvert.push(headerRow);
        }

        ///construct item row data on every pass with paged data
        if (!_.isUndefined(list)) {
          _.map(list, function (inv) {
            var singleRow = [];

            _.each(fields, function (val, key) {
              var txt = "";

              ///use view function to format data else return value
              if (typeof fields[key].view === "function") {
                txt =
                  fields[key].view(null, inv) !== undefined
                    ? fields[key].view(null, inv)
                    : "";

                return singleRow.push('"' + txt + '"');
              } else {
                var retVal = inv[key];

                ///if field type is a date, additional formatting
                if (val.hasOwnProperty("type") && val.type == "date") {
                  retVal = moment(inv[key]).format("MM/DD/YYYY");
                }

                return singleRow.push('"' + retVal + '"');
              }
            });

            dataToConvert.push(singleRow);
          });
        }

        if (options.metrics) {
          ///sum data calculations
          _.each(options.metrics, function (opt, item) {
            ///grab metric row title
            if (metricRow.indexOf(opt.title) < 0) metricRow.push(opt.title);

            if (opt.fields) {
              ///construct memo object to keep running total of metrics
              _.mapObject(opt.fields, function (v, k) {
                if (typeof v === "function") {
                  ///call metric view function to format data
                  if (!memo[k]) {
                    memo[k] = v(null, list, true);
                  } else {
                    memo[k] = memo[k] + v(null, list, true);
                  }
                } else if (
                  opt &&
                  opt.fields &&
                  typeof opt.fields[k] &&
                  typeof opt.fields[k].view === "function"
                ) {
                  memo[k] = opt.fields[k].view(null, list, true);
                }
              });
            }
          });
        }

        return;
      }

      function build_csv_doc(memo) {
        ///construct metric row data for csv document
        _.mapObject(memo, function (v, k) {
          ///find index in relation to header row columns
          var index = _.findIndex(headerRow, function (rowCol) {
            return rowCol == '"' + k + '"';
          });

          // If not found, check against field
          if (index == -1) {
            if (fieldsClone && fieldsClone[k]) {
              index = _.findIndex(headerRow, function (rowCol) {
                return rowCol == '"' + fieldsClone[k].title + '"';
              });
            }
          }

          if (index > 0) {
            ///use metrics view fn to format data

            if (typeof options.metrics.sum.fields[k] === "function") {
              metricRow.splice(
                index,
                0,
                '"' + options.metrics.sum.fields[k](null, v, false) + '"'
              );
            } else if (
              typeof options.metrics.sum.fields[k].view === "function"
            ) {
              metricRow.splice(
                index,
                0,
                '"' + options.metrics.sum.fields[k].view(null, v, false) + '"'
              );
            }
          } else {
            metricRow.push(" ,");
          }
        });

        ///add metricRow to data to convert collection
        dataToConvert.push(metricRow);

        var csvContent = "data:text/csv;charset=utf-8,";

        ///each over data to build csv formatted data
        dataToConvert.forEach(function (infoArray, index) {
          if (infoArray == headerRow) {
            infoArray = _.map(infoArray, function (header) {
              header = header.replace(/['"]+/g, "");
              header = header.replace(/_/g, " ");

              return (header = header[0].toUpperCase() + header.slice(1));
            });
          }

          dataString = infoArray.join("\t");

          csvContent += dataString.replace(/#/g, "") + "\n";
        });

        var encodedUri = encodeURI(csvContent);
        var link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "download.tsv");
        document.body.appendChild(link); // Required for FF

        link.click();

        return;
      }

      pageThroughData(setup, build_csv_row, build_csv_doc);
    }

    function download_pdf(setup) {
      var pageObject = {
        page: 1,
        sortCol: "date_created",
        sortDir: "desc",
        sortCast: "date",
        pageLength: 50,
      };

      var html = "";
      var pagingState = options.showPaging;

      setup.page = pageObject;

      pageThroughData(
        setup,
        function (list, memo, triggerNextPage) {
          options.showPaging = false;
          View.Body.empty();
          body_ui(View.Body, options, list, false, function () {
            html += $(View.Body.selector).html();
            html += "<hr>";
            triggerNextPage();
          });
          View.Body.patch();
        },
        function () {
          options.showPaging = pagingState;
          sb.data.makePDF(html, "I");
        }
      );
    }

    function tag_selected_items(onComplete, itemId) {
      if (_.isEmpty(CurrentSelection) && itemId === undefined) {
        sb.dom.alerts.alert(
          "No items selected",
          "Select some items first.",
          "warning"
        );
        return false;
      }

      var itemsToTag = CurrentSelection;
      if (itemId !== undefined && Array.isArray(itemId)) {
        itemsToTag = itemId;
      }

      View["large-modal"].body.empty();
      View["large-modal"].body.makeNode("tags", "div", {
        style: "min-height:300px;",
      });
      View["large-modal"].body.patch();

      if (Comps.hasOwnProperty("tags")) {
        Comps.tags.destroy();
      }

      View["large-modal"].show();

      largeModalState = "tags";

      // get shared tags
      var tmp = _.chain(Data)
        .filter(function (obj) {
          return _.contains(itemsToTag, obj.id);
        })
        .pluck("tagged_with")
        .intersection()
        .value();

      var sharedTags = _.intersection.apply(_, tmp);

      Comps.tags = sb.createComponent("tags");
      Comps.tags.notify({
        type: "object-tag-view",
        data: {
          domObj: View["large-modal"].body.tags,
          objectType: options.objectType,
          objectId: itemsToTag,
          tags: sharedTags,
          onUpdate: function () {},
        },
      });
    }

    function update_selected_items(onComplete, itemId) {}

    // data funcs

    function cache_blueprint(objectType, callback, options) {
      if (!_.isEmpty(options.entityType)) {
        Blueprint = options.entityType.blueprint;
        EntityType = options.entityType;

        callback(options.entityType.blueprint);
      } else {
        sb.data.db.obj.getBlueprint(
          options.objectType,
          function (bp) {
            Blueprint = bp;
            callback(bp);
          },
          false
        );
      }
    }

    function find_type_type(options, blueprint) {
      function shouldGetTypesFromOptions(options) {
        var ret = false;

        _.each(options.fields, function (field, fieldName) {
          if (field.type === "state" && _.isEmpty(field.workflow)) {
            if (field.typeField) {
              ret = field.typeField;
            } else {
              // default to 'type' value
              ret = "type";
            }
          } else if (field.type === "type") {
            ret = fieldName;
          }
        });

        return ret;
      }

      var shouldGetTypes = shouldGetTypesFromOptions(options);
      if (!shouldGetTypes) {
        return false;
      }
      var objType = blueprint[shouldGetTypes].objectType;

      // if we're working with a group, check for different
      // type sets between, projects or tasks
      if (
        options.objectType === "groups" &&
        options.where &&
        options.where.hasOwnProperty("group_type")
      ) {
        switch (options.where.group_type) {
          case "Headquarters":
          case "Team":
            break;

          case "Task":
            objType = "task_types";
            break;

          case "Project":
            objType = "project_types";
            break;
        }
      }

      return objType;
    }

    function get_types(options, callback, blueprint) {
      if (EntityType) {
        callback(options.subTypes);
      } else {
        var objType = find_type_type(options, blueprint);
        if (!objType) {
          return callback([]);
        }

        sb.data.db.obj.getAll(
          objType,
          function (types) {
            callback(types);
          },
          {
            name: true,
            states: true,
            is_default_type: true,
            template: {
              name: true,
            },
          }
        );
      }
    }

    ///recursive function to alter paged data. work done inside of onPage. onComplete when finished. pass memo Obj for state control
    function pageThroughData(setup, onPage, onComplete, memo) {
      var memo = memo || {};

      get_data(
        setup.options,
        setup.Query,
        function (list, response) {
          if (_.isEmpty(list) || _.isUndefined(list)) {
            onComplete(memo);
          } else {
            // Async version
            if (onPage.length === 3) {
              onPage(list, memo, function () {
                setup.page.page++;
                pageThroughData(setup, onPage, onComplete, memo);
              });
            } else {
              onPage(list, memo);
              setup.page.page++;
              pageThroughData(setup, onPage, onComplete, memo);
            }
          }
        },
        setup.page,
        setup.Types,
        setup.ViewType,
        null,
        false,
        undefined
      );
    }

    // utility funcs

    function createGroup(data, customView, callback, options, btn, group) {
      var currentWorkflow = [];

      if (EntityType) {
        currentWorkflow = EntityType.blueprint[options.groupBy.field].workflow;
      } else {
        currentWorkflow = _.findWhere(Types, { id: Query.type });
      }

      var currentState = group;

      sb.notify({
        type: "update-state-flow",
        data: {
          before: currentState,
          operation: "add",
          type: currentWorkflow,
          onComplete: function (updatedStates) {
            if (EntityType) {
              EntityType.blueprint[options.groupBy.field].workflow.states =
                updatedStates;
            } else {
              _.findWhere(Types, { id: Query.type }).states = updatedStates;
            }

            var groupBy = undefined;
            if (options.groupBy && options.groupBy.field) {
              groupBy = options.groupBy.field;
            }

            setGroupBy(options, groupBy);

            if (callback) {
              callback(true);
            } else {
              ui.Body.empty();
              body_ui(ui.Body, options, Data);
              ui.Body.patch();
            }

            sb.notify({
              type: "edit-state-form",
              data: {
                state: updatedStates[updatedStates.length - 1].id,
                modal: View["large-modal"],
                type: currentWorkflow,
                onComplete: function (updated) {
                  if (EntityType) {
                    EntityType.blueprint[
                      options.groupBy.field
                    ].workflow.states = updated.states;
                  } else {
                    _.findWhere(Types, { id: Query.type }).states =
                      updated.states;
                  }

                  var groupBy = undefined;
                  if (options.groupBy && options.groupBy.field) {
                    groupBy = options.groupBy.field;
                  }

                  setGroupBy(options, groupBy);

                  if (callback) {
                    callback(true);
                  } else {
                    ui.Body.empty();
                    body_ui(ui.Body, options, Data);
                    ui.Body.patch();
                  }
                },
              },
            });
          },
        },
      });
    }

    function getNewSeed(templateObj) {
      if (templateObj === undefined) {
        templateObj = {};
      } else {
        templateObj = templateObj;
      }

      var toTagArray = Query.tagged_with.slice();
      toTagArray.push(parseInt(sb.data.cookie.userId));
      toTagArray = _.uniq(toTagArray);

      templateObj.object_bp_type = options.objectType;
      templateObj.tagged_with = toTagArray;

      if (teamTag) templateObj.tagged_with.push(teamTag);

      templateObj.tagged_with = _.uniq(templateObj.tagged_with);
      templateObj.is_template = 0;

      // set the selected type, or default if none is selected
      if (getTypeField(options.fields).field) {
        if (_.findWhere(Types, { is_default_type: 1 })) {
          templateObj[getTypeField(options.fields).field] = _.findWhere(Types, {
            is_default_type: 1,
          }).id;
        }

        if (Query.type) {
          templateObj[getTypeField(options.fields).field] = Query.type;
        }
      }

      if (Query.templates) {
        templateObj.is_template = 1;
      }

      if (
        options &&
        options.where &&
        typeof options.where.parent === "number"
      ) {
        templateObj.parent = options.where.parent;
      }

      if (parseInt(options.tool) > 0) {
        templateObj.tagged_with.push(options.tool);
      }
      if (parseInt(options.parent) > 0 || _.isObject(options.parent)) {
        templateObj.tagged_with.push(options.parent);
        templateObj.parent = options.parent;
      }
      if (parseInt(options.entity) > 0) {
        templateObj.parent = options.entity;
      }

      return templateObj;
    }

    function onLargeModalClose() {
      if (largeModalState === "tags") {
        refresh_pool(ui.Body, options);
      }

      largeModalState = "closed";

      if (ui["large-modal"].body && ui["large-modal"].footer) {
        ui["large-modal"].body.empty();
        ui["large-modal"].footer.empty();
        ui["large-modal"].body.patch();
        ui["large-modal"].footer.patch();
      }

      return;
    }

    function run_search(searchTerm) {
      // show loader
      $("#loader").fadeIn();

      // update the state
      Query.search.search_term = searchTerm;
      Query.search.isActive = !_.isEmpty(searchTerm);

      // refresh the data/ui
      refresh_pool(View.Body, options);
    }

    function search_ui(ui, options) {
      var searchInput;
      var searchNode;

      if (options.searchAboveCollection) {
        ui.makeNode("search", "div", {
          css: "item",
          style:
            "width:100%; padding-right:0px; margin-bottom:10px; border:1px solid #ebebeb; border-radius:0.375rem;",
        });
        ui.search.makeNode("input", "div", {
          css: "ui fluid icon input",
          style: "width:100%;",
        });
        ui.search.input.makeNode("input", "div", {
          tag: "input",
          placeholder: "Search...",
          icon: "search",
          style: "border:none;",
        });

        searchInput = ui.search.input.input;
        searchNode = ui.search.input.input;

        searchNode.notify(
          "change",
          {
            type: "collections-run",
            data: {
              run: function () {
                var searchVal = $(this.selector).val();
                run_search(searchVal);
              }.bind(searchInput),
            },
          },
          sb.moduleId
        );

        ui.patch();
      } else {
        ui.makeNode("search", "div", {
          css: "right floated item",
          style: "min-width:220px;padding-right:0px;border:none;",
        });
        ui.search.makeNode("input", "div", {
          css: "ui fluid icon input pull-right",
          style: "width:25%;",
        });
        ui.search.input.makeNode("input", "div", {
          tag: "input",
          placeholder: "Search...",
          icon: "search",
          style: "border:none;",
        });

        searchInput = ui.search.input.input;
        searchNode = ui.search.input.input;

        searchNode.notify(
          "change",
          {
            type: "collections-run",
            data: {
              run: function () {
                var searchVal = $(this.selector).val();
                run_search(searchVal);
              }.bind(searchInput),
            },
          },
          sb.moduleId
        );

        ui.patch();
      }
    }

    function setGroupBy(options, val) {
      // when triggered by subview change.
      if (
        val == null &&
        ViewType.list !== false &&
        _.isEmpty(options.groupings)
      ) {
        options.groupBy = {
          field: "type",
          title: "Type",
        };
        ViewState.isGrouping = false;
        return;
      }

      ViewState.isGrouping = true;

      if (options.groupings && typeof options.groupings === "object") {
        if (typeof val !== "undefined") {
          options.groupBy = {
            field: val,
            title: options.groupings[val],
          };
        } else {
          if (
            options.hasOwnProperty("subviews") &&
            typeof options.subviews === "object" &&
            options.subviews.hasOwnProperty(ViewType.name) &&
            typeof options.subviews[ViewType.name] !== "undefined" &&
            options.subviews[ViewType.name].groupBy &&
            options.subviews[ViewType.name].groupBy.hasOwnProperty("defaultTo")
          ) {
            options.groupBy = {
              field: options.subviews[ViewType.name].groupBy.defaultTo,
              title:
                options.groupings[
                  options.subviews[ViewType.name].groupBy.defaultTo
                ],
            };
          } else {
            options.groupBy = {
              field: Object.keys(options.groupings)[0],
              title: options.groupings[Object.keys(options.groupings)[0]],
            };
          }
        }

        if (
          options.groupings[options.groupBy.field] &&
          options.groupings[options.groupBy.field].group
        ) {
          options.groupBy.title =
            options.groupings[options.groupBy.field].title;
          options.groupBy.group =
            options.groupings[options.groupBy.field].group;
          options.groupBy.options =
            options.groupings[options.groupBy.field].options;
        }

        // if in a grouping view, and grouped by a state, filter by the state's type obj
        if (
          options.groupBy &&
          subviewShouldGroup(ViewType) &&
          options.fields[options.groupBy.field] &&
          options.fields[options.groupBy.field].type === "state"
        ) {
          if (!Query.type && Types[0] && !options.entityType) {
            Query.type = Types[0].id;
          }

          var tmp = _.findWhere(Types, { id: Query.type });

          if (
            Blueprint[options.groupBy.field] &&
            !_.isEmpty(Blueprint[options.groupBy.field].workflow) &&
            typeof Blueprint[options.groupBy.field].workflow === "object"
          ) {
            tmp = Blueprint[options.groupBy.field].workflow;
          }

          options.groupBy.options = [
            {
              name: "Not set",
              value: null,
              color: "grey",
            },
          ];

          if (tmp && tmp.states) {
            var state = _.findWhere(tmp.states, { isEntryPoint: 1 });

            while (state != undefined) {
              var stateObj = {
                name: state.name,
                value: state.uid,
                color: state.color,
                isDone: 0,
                isEntryPoint: state.isEntryPoint,
              };

              if (state.type === "done") {
                stateObj.isDone = 1;
              }

              options.groupBy.options.push(stateObj);

              state = _.findWhere(tmp.states, { uid: parseInt(state.next[0]) });
            }
          }

          return;
        }
      } else {
        options.groupBy = _.clone(getTypeField(options.fields));

        return;
      }
    }

    function subviewShouldGroup(subview) {
      if (subview.groups) {
        return true;
      }

      if (
        subview.canGroup &&
        options.subviews &&
        options.subviews[subview.id] &&
        options.subviews[subview.id].groupBy
      ) {
        return true;
      }

      return false;
    }

    function toggleSort(key, dir) {
      var oldKey = Page.sortCol;
      var oldDir = Page.sortDir;

      if (typeof dir === "undefined") {
        if (key === Page.sortCol && Page.sortDir === "asc") {
          dir = "desc";
        } else {
          dir = "asc";
        }
      }

      // update ViewState.sortBy
      Page.sortCol = key;
      Page.sortDir = dir;

      switch (Blueprint[key].type) {
        case "date":
          Page.sortCast = "date";
          break;

        case "objectId":
          Page.sortCast = "objectId";
          break;

        default:
          Page.sortCast = "string";
          break;
      }

      options.sortCast = Page.sortCast;
      options.sortCol = Page.sortCol;
      options.sortDir = Page.sortDir;

      refresh_pool(View.Body, options);

      if (typeof UpdateSortBtns === "function") {
        UpdateSortBtns(Page.sortCol, Page.sortDir, oldKey, oldDir);
      }
    }

    function templateForm_ui(ui, setup, callback) {
      var templateFormArgs = {};
      var fieldOpt = setup.options["fields"];

      if (setup.object.hasOwnProperty("id")) {
        sb.data.db.obj.getById("", setup.object.id, function (resp) {
          _.mapObject(fieldOpt, function (optVal, optKey, list) {
            if (optVal.type == "title" || optVal.type == "date") {
              var fieldType;
              var placeholder = "";
              var defaultValue = "";

              if (optVal.type == "date") {
                fieldType = "date";
              } else if (optVal.type == "title") {
                fieldType = "text";
              }

              if (resp.hasOwnProperty([optKey])) {
                placeholder = resp[optKey];
              }

              templateFormArgs[optKey] = {
                name: optKey,
                type: fieldType,
                label: optVal.title,
                placeholder: placeholder,
              };
            }
          });

          if (setup.object.hasOwnProperty("name") && setup.object.name !== "") {
            templateFormArgs.name.value = setup.object.name;
          }

          if (!_.isEmpty(templateFormArgs)) {
            ui.empty();
            ui.makeNode("btns", "div", { css: "ui right floated buttons" });

            ui.btns
              .makeNode("back", "div", {
                css: "ui button",
                text: '<i class="angle left icon"></i>Choose different Template',
              })
              .notify(
                "click",
                {
                  type: "collections-run",
                  data: {
                    run: function (ui, setup) {
                      ui.empty();

                      if (setup.hasOwnProperty("back")) {
                        setup.back(ui);
                      } else {
                        viewTemplateSelect(ui, setup);
                      }
                    }.bind(null, ui, setup),
                  },
                },
                sb.moduleId
              );

            ui.btns.makeNode("save", "div", {
              css: "ui green button",
              text: '<i class="check circle icon"></i> Save Changes',
            });

            ui.makeNode("header", "div", {
              tag: "h2",
              css: "ui header",
              text:
                '<i class="window restore outline icon"></i>' +
                '<div class="content">Update New Item' +
                '<div class="sub header">Make changes to the fields below to create your new item.</div>' +
                "</div>",
            });

            ui.makeNode("form", "form", templateFormArgs);

            ui.btns.save.notify(
              "click",
              {
                type: "collections-run",
                data: {
                  run: function (ui, setup) {
                    ui.btns.save.loading();

                    process_createFromTemplate(
                      resp,
                      ui,
                      setup,
                      function (templateOptions) {
                        callback(templateOptions);

                        /*
sb.data.db.obj.createFromTemplate(resp.id, function(response){

											if(response) {

												callback(response);

											}

										}, 0, templateOptions);
*/
                      }
                    );
                  }.bind(null, ui, setup),
                },
              },
              sb.moduleId
            );

            ui.patch();
          } else {
            callback(false);
          }
        });
      } else {
        callback(false);
      }
    }

    function process_createFromTemplate(template, ui, setup, cb) {
      var formData = ui.form.process();
      var templateOptions = {
        //validated: false
      };

      _.map(formData.fields, function (v, k, l) {
        if (v.value !== "") {
          templateOptions[k] = v.value;
          //templateOptions.validated = true;
        } else {
          templateOptions[k] = template[k];
          //templateOptions.validated = true;
        }
      });

      //if(templateOptions.validated){

      cb(templateOptions);

      /*
} else {

					sb.dom.alerts.alert('', 'All form fields are required', 'warning');
				}
*/
    }

    // views

    function header_ui(ui, options, pools) {
      function setup_default_filters(options) {
        if (ViewType) {
          // if in a grouping view, and grouped by a state, filter by the state's type obj
          if (
            !State.typeFilter &&
            options.groupBy &&
            subviewShouldGroup(ViewType) &&
            options.fields[options.groupBy.field] &&
            options.fields[options.groupBy.field].type === "state"
          ) {
            if (Types[0]) {
              Query.type = Types[0].id;
            }

            State.typeFilter = Query.type;

            /*
Query.filters.push({
							field:'type',
							value:Types[0].id,
							type:'equal_to'
						});
*/

            return true;
          }
        }

        return false;
      }

      function menu_ui(ui, options, state) {
        function labels_ui(ui, query) {
          ui.empty();
          if (query.templates) {
            ui.makeNode("tem", "div", {
              css: "ui right floated circular icon item button",
              text: '<i class="window restore outline icon"></i>',
              tooltip: {
                title: "Templates",
                text: "You have currently toggled Template View. You can view any templates you have made below.",
                position: "left center",
              },
              listener: {
                type: "popup",
                hoverable: true,
              },
            });
          }

          if (query.archive) {
            ui.makeNode("arch", "div", {
              css: "ui right floated stackable circular icon item button",
              text: '<i class="archive icon"></i>',
              tooltip: {
                title: "Archive",
                text: "You have currently toggled Archive View. You can view any items you have placed in your archive.",
                position: "left center",
              },
              listener: {
                type: "popup",
                hoverable: true,
              },
            });
          }

          ui.patch();
        }

        function select_subview(ui, view, state) {
          if (options.onBoxview === undefined || options.onBoxview === false) {
            // !CACHING - caching subview
            update_browserCache(options, function (data, callback) {
              data.subview = view.name;

              callback(data);
            });
          }

          CurrentSelection = [];
          var previousType = state.type;
          if (view) {
            state.type = view.name;
            ViewType = _.findWhere(views, {
              name: state.type,
            });
          }

          // update menu item css
          $(ui.selector).parent().find(".active").removeClass("active");
          ui.addClass("active");

          // update subview specific options
          if (
            options.subviews &&
            options.subviews[state.type] &&
            options.subviews[state.type].options
          ) {
            options = resetOptions(options.subviews[state.type].options);
          } else {
            options = resetOptions(OptionSets._default);
          }

          // update subview specific defaults based on collection options
          if (options.subviews && options.subviews[ViewType.name]) {
            if (
              options.subviews[ViewType.name].range &&
              options.subviews[ViewType.name].range.defaultTo &&
              ui.time_range
            ) {
              Query.range = options.subviews[ViewType.name].range.defaultTo;
            }

            if (
              options.subviews[ViewType.name].groupBy &&
              options.subviews[ViewType.name].groupBy.defaultTo
            ) {
              setGroupBy(
                options,
                options.subviews[ViewType.name].groupBy.defaultTo
              );
            }
          }

          // Refresh data.
          // !TEMPORARILY COMMENTING THIS OUT
          // refresh_pool (View.Body, options);

          // update the sub-menu
          container.submenu.empty();
          submenu_ui(container.submenu, options, State);

          refresh_pool(View.Body, options);

          container.submenu.patch();
        }

        if (_.isEmpty(options.menu) || options.menu.subviews !== false) {
          _.chain(views)
            .filter(function (view) {
              if (
                (view.hidden &&
                  (_.isEmpty(options.subviews) ||
                    !options.subviews.hasOwnProperty(view.name))) ||
                (options.objectType.startsWith("#") &&
                  !_.isEmpty(options.subviews) &&
                  !_.contains(options.subviews, view.name))
              ) {
                return false;
              }

              // hide subviews based on menu
              if (
                !_.isEmpty(options.menu) &&
                !_.isEmpty(options.menu.subviews)
              ) {
                return options.menu.subviews[view.name];
              }

              return true;
            })
            .each(function (view, i) {
              var css = " item";

              // Set subview from collections config object
              if (options.config !== undefined) {
                if (options.config.hasOwnProperty("subview")) {
                  if (options.config.subview !== "default") {
                    state.type = options.config.subview;
                  }
                }
              }

              if (state.type === view.name) {
                css = "item active";
              }

              var txt = '<i class="' + view.icon + ' icon"></i> ' + view.title;
              if (OnMobile) {
                txt = '<i class="' + view.icon + ' icon"></i>';
              }

              ui.makeNode("v-" + i, "div", {
                tag: "a",
                text: txt,
                css: css,
                // 							, style: 'background-color:white;background:white;'
              });

              ui["v-" + i].notify(
                "click",
                {
                  type: "collections-run",
                  data: {
                    run: select_subview.bind({}, ui["v-" + i], view, state),
                  },
                },
                sb.moduleId
              );
            });
        }

        // right options (options of current view)
        var right = ui.makeNode("viewOptions", "div", {
          css: "ui right floated secondary tiny stackable menu",
        });

        /*
right.makeNode(
					'lastUpdated'
					, 'div'
					, {
						text: ''
						, css: 'secondary item'
					}
				);
				updateLastRefreshUi = function () {

					$(this.selector).text('Refreshed just now');

				}.bind(right.lastUpdated);
*/

        right.makeNode("labels", "div", { css: "ui right floated labels" });

        if (!appConfig.is_portal) {
          batch_actions_ui(
            options,
            right,
            undefined,
            pools,
            undefined,
            labels_ui.bind({}, right.labels),
            {
              dropdownCss:
                "ui right floated stackable circular icon dropdown basic item",
            }
          );
        }
      }

      function submenu_ui(ui, options, state) {
        var currentType = {};
        var currentState = {};
        var currentRole = {};
        var currentCategory = {};

        function date_range_prop_ui(ui, options) {
          var placeholder = "Created on";
          var datePropOptions = [];

          _.each(options.fields, function (field, key) {
            if (field.rangeOver && !options.rangeOver) {
              options.rangeOver = key;
            }

            if (field.type === "date") {
              datePropOptions.push({
                name: field.title,
                value: key,
                selected: options.rangeOver === key,
              });

              if (options.rangeOver === key) {
                placeholder = field.title;
              }
            }
          });

          datePropOptions.push({
            name: "Created on",
            value: "date_created",
            selected: false,
          });

          if (datePropOptions.length <= 1) {
            return;
          }

          ui.makeNode("dateRangeProp", "div", {
            text:
              '<i class="clock icon"></i><span class="text">' +
              placeholder +
              '</span><i class="dropdown icon"></i>',
            css: "ui dropdown transparent button",
            listener: {
              onChange: function (value, text) {
                if (value && value !== Query.range) {
                  if (
                    (options.fields[value] || value === "date_created") &&
                    options.rangeOver !== value
                  ) {
                    options.rangeOver = value;
                    refresh_pool(View.Body, options);
                  }
                }
              },
              type: "dropdown",
              values: datePropOptions,
              placeholder: placeholder,
            },
          });
        }

        function filters_ui(ui, shouldPatch) {
          function get_filter_type_options(key, fields, Blueprint) {
            if (Blueprint[key]) {
              switch (Blueprint[key].type) {
                case "string":
                  return [
                    {
                      name: "<strong>contains</strong>",
                      value: "contains",
                      selected: true,
                    },
                    {
                      name: "<strong>is</strong>",
                      value: "equal_to",
                      selected: true,
                    },
                  ];
                  break;

                case "int":
                  return [
                    {
                      name: "<strong>=</strong>",
                      value: "equal_to",
                      selected: true,
                    },
                    {
                      name: "<strong><</strong>",
                      value: "less_than",
                      selected: true,
                    },
                    {
                      name: "<strong>></strong>",
                      value: "greater_than",
                      selected: true,
                    },
                  ];
                  break;
              }
            }

            if (key && fields[key].type === "type") {
              return [
                {
                  name: "<strong>=</strong>",
                  value: "equal_to",
                  selected: true,
                },
              ];
            }

            return [];
          }

          function single_filter_ui(ui, filter, i) {
            function filter_value_ui(ui, filter, shouldPatch) {
              if (
                filter.field &&
                options.fields[filter.field].type === "type"
              ) {
                ui.makeNode("value", "div", {}).makeNode("value", "div", {
                  text: '<span class="text"><strong>Types</strong></span>',
                  css: "ui dropdown item",
                  listener: {
                    onChange: function (i, value) {
                      if (value) {
                        Query.filters[i].value = parseInt(value);
                        refresh_pool(View.Body, options);
                      }
                    }.bind({}, i),
                    type: "dropdown",
                    values: _.map(Types, function (type, i) {
                      return {
                        name: type.name,
                        value: type.id,
                        selected: i === 0,
                      };
                    }),
                    placeholder: "Compare",
                  },
                });
              } else {
                ui.makeNode("value", "div", {
                  css: "item",
                })
                  .makeNode("input", "div", {
                    css: "ui transparent input",
                    text:
                      '<input type="text" value="' +
                      filter.value +
                      '" placeholder="Type...">',
                  })
                  .listeners.push(
                    function (i, selector) {
                      $(selector).on("change", function () {
                        Query.filters[i].value = $(this)
                          .children()
                          .first()
                          .val();
                        refresh_pool(View.Body, options);
                      });
                    }.bind({}, i)
                  );
              }

              if (shouldPatch) {
                ui.value.patch();
              }
            }

            var fieldOptions = [];
            _.each(options.fields, function (field, key) {
              if (
                (Blueprint[key] &&
                  (Blueprint[key].type === "string" ||
                    Blueprint[key].type === "int")) ||
                field.type === "type"
              ) {
                var isSelected = filter.field === key;

                fieldOptions.push({
                  name:
                    '<strong><i class="' +
                    getFieldTypeIcon(key, options.fields, Blueprint) +
                    ' icon"></i> ' +
                    field.title +
                    "</strong>",
                  value: key,
                  selected: isSelected,
                });
              }
            });

            // field
            ui.makeNode("field", "div", {
              text: '<span class="text"><strong>Field</strong></span>',
              css: "ui dropdown item",
              listener: {
                onChange: function (i, value) {
                  if (value) {
                    Query.filters[i].field = value;

                    $(ui.type.selector).dropdown(
                      "change values",
                      get_filter_type_options(value, options.fields, Blueprint)
                    );
                    // 									refresh_pool (View.Body, options);

                    filter_value_ui(ui, filter, true);
                  }
                }.bind({}, i),
                type: "dropdown",
                values: fieldOptions,
                placeholder: "Field",
              },
            });

            // type
            ui.makeNode("type", "div", {
              text: filter.type,
              css: "item",
            });
            ui.makeNode("type", "div", {
              text: '<span class="text"><strong>Compare</strong></span>',
              css: "ui dropdown item",
              listener: {
                onChange: function (i, value) {
                  if (value) {
                    Query.filters[i].type = value;
                    Query.filters[i].value = "";
                    refresh_pool(View.Body, options);
                  }
                }.bind({}, i),
                type: "dropdown",
                values: get_filter_type_options(
                  filter.field,
                  options.fields,
                  Blueprint
                ),
                placeholder: "Compare",
              },
            });

            // value
            filter_value_ui(ui, filter);

            // remove filter
            ui.makeNode("rm", "div", {
              text: '<i class="remove icon"></i>',
              css: "ui red icon mini right floated button inverted item",
            }).notify(
              "click",
              {
                type: "collections-run",
                data: {
                  run: function (filters, i) {
                    this.empty();
                    this.patch();

                    filters.splice(i, 1);
                    filters_list_ui(this, filters);
                    this.patch();

                    refresh_pool(View.Body, options);
                  }.bind(filtersUI, Query.filters, i),
                },
              },
              sb.moduleId
            );
          }

          function filters_list_ui(ui, filters) {
            _.each(filters, function (filter, i) {
              single_filter_ui(
                ui.makeNode("filter-" + i, "div", {
                  css: "ui fluid secondary menu",
                  style: "min-width:500px !important;",
                }),
                filter,
                i
              );

              ui.makeNode("br-" + i, "div", {
                css: "ui clearing divider",
                text: "<br />",
              });
            });
          }

          ui.makeNode("filters", "div", {
            text: '<i class="filter icon"></i>',
            css: "ui item",
            tag: "a",
            listener: {
              type: "popup",
              inline: true,
              preserve: true,
              hoverable: true,
              position: "left center",
              transition: "fade",
              distanceAway: -20,
            },
          });

          var filtersUI = ui
            .makeNode("filter-popup", "div", {
              // 					css:'ui popup bottom left transition fluid hidden'
              css: "ui popup bottom left transition hidden",
            })
            .makeNode("c", "div", {});

          filters_list_ui(filtersUI, Query.filters);

          ui["filter-popup"]
            .makeNode("addFilter", "div", {
              text: '<i class="plus icon"></i>',
              tag: "button",
              css: "ui icon mini left floated button",
            })
            .notify(
              "click",
              {
                type: "collections-run",
                data: {
                  run: function (filters) {
                    this.empty();
                    this.patch();

                    filters.push({ value: "" });
                    filters_list_ui(this, filters);
                    this.patch();
                  }.bind(filtersUI, Query.filters),
                },
              },
              sb.moduleId
            );

          if (shouldPatch) {
            ui["filter-popup"].patch();
          }
        }

        function options_ui(ui, Query, pools) {
          // utility functions

          function getSortBtnCss(key, dir) {
            if (Page.sortCol === key && Page.sortDir === dir) {
              return "teal link icon active item";
            }

            return "teal link icon item";
          }

          function toggleSortSelection(key, dir) {
            toggleSort(key, dir);
          }

          // ui functions

          ui.makeNode("options", "div", {
            text: '<i class="columns icon"></i> Visible Columns',
            css: "ui item",
            tag: "a",
            listener: {
              type: "popup",
              inline: true,
              preserve: true,
              hoverable: true,
              position: "left center",
              transition: "fade",
              distanceAway: -20,
            },
          });

          ui.makeNode("popupContainer", "div", {
            css: "ui popup bottom left transition fluid hidden",
          });

          var i = 0;
          _.each(options.fields, function (field, key) {
            // place divider
            if (i > 0) {
              ui.popupContainer.makeNode("br-" + key, "div", {
                css: "ui clearing divider",
                text: "<br />",
              });
            }

            // toggle field visibility
            var selectedText = ' checked=""';
            if (_.contains(ViewState.hiddenFields, key)) {
              selectedText = "";
            }

            var icon = getFieldTypeIcon(key, options.fields, Blueprint);

            ui.popupContainer.makeNode("field-" + key, "div", {
              text:
                '<input type="checkbox"' +
                selectedText +
                '><label><i class="text-muted ' +
                icon +
                ' icon"></i>' +
                field.title +
                "</label>",
              css: "ui slider checkbox",
              listener: {
                type: "checkbox",
                onChecked: function (fieldName) {
                  ViewState.hiddenFields = _.reject(
                    ViewState.hiddenFields,
                    function (hiddenField) {
                      return hiddenField === fieldName;
                    }
                  );
                  body_ui(View.Body, options, Data);
                  View.Body.patch();
                }.bind({}, key),
                onUnchecked: function (fieldName) {
                  ViewState.hiddenFields.push(fieldName);
                  body_ui(View.Body, options, Data);
                  View.Body.patch();
                }.bind({}, key),
              },
            });

            // if field corresponds to a blueprint property definition,
            // allow for some query management on that field

            if (Blueprint[key]) {
              // sort btns
              var sortCss = getSortBtnCss(key, "asc");

              ui.popupContainer.makeNode("sort-btns-" + key, "div", {
                css: "ui right floated text tiny menu",
              });

              ui.popupContainer["sort-btns-" + key]
                .makeNode("asc", "div", {
                  css: sortCss,
                  text: '<i class="arrow up icon"></i>',
                })
                .notify(
                  "click",
                  {
                    type: "collections-run",
                    data: {
                      run: toggleSortSelection.bind({}, key, "asc"),
                    },
                  },
                  sb.moduleId
                );

              sortCss = getSortBtnCss(key, "desc");

              ui.popupContainer["sort-btns-" + key]
                .makeNode("desc", "div", {
                  css: sortCss,
                  text: '<i class="arrow down icon"></i>',
                })
                .notify(
                  "click",
                  {
                    type: "collections-run",
                    data: {
                      run: toggleSortSelection.bind({}, key, "desc"),
                    },
                  },
                  sb.moduleId
                );
            }

            i++;
          });

          UpdateSortBtns = function (key, dir, oldKey, oldDir) {
            // remove "active" class from old selection
            if (this.popupContainer["sort-btns-" + oldKey]) {
              this.popupContainer["sort-btns-" + oldKey][oldDir].removeClass(
                "active"
              );
            }

            // add "active" class to new selection
            this.popupContainer["sort-btns-" + key][dir].addClass("active");
          }.bind(ui);
        }

        function types_ui(ui, options) {
          function editTypeModal(modal, types, onComplete, type) {
            modal.body.empty();
            modal.footer.empty();

            var formArgs = {
              name: {
                name: "name",
                type: "text",
                label: "Type name",
              },
            };

            function saveNewWorkflow(data) {
              modal.footer.save.loading();
              var formData = modal.body.form.process().fields;

              // if we're working with a group, check for different
              // type sets between, projects or tasks
              if (
                options.objectType === "groups" &&
                options.where &&
                options.where.hasOwnProperty("group_type")
              ) {
                switch (options.where.group_type) {
                  case "Headquarters":
                  case "Team":
                    break;

                  case "Task":
                    objType = "task_types";
                    break;

                  case "Project":
                    objType = "project_types";
                    break;
                }
              }

              if (_.isEmpty(type)) {
                sb.data.db.obj.create(
                  objType,
                  {
                    name: formData.name.value,
                  },
                  function (response) {
                    modal.hide();
                    modal.footer.save.loading(false);
                    onComplete(response);
                  }
                );
              } else {
                sb.data.db.obj.update(
                  objType,
                  {
                    id: type.id,
                    name: formData.name.value,
                    template: type.template.id,
                  },
                  function (response) {
                    modal.hide();
                    modal.footer.save.loading(false);
                    onComplete(response);
                  }
                );
              }
            }

            if (typeof options.editSubType === "function") {
              modal.show();
              options.editSubType(modal.body, type, function (newType) {
                modal.hide();
                onComplete(newType);
              });
              return;
            }

            if (!_.isEmpty(type)) {
              formArgs.name.value = type.name;
            }

            modal.body.makeNode("form", "form", formArgs);

            // template to use when creating a project
            if (
              options.objectType === "groups" &&
              options.where.group_type === "Project" &&
              !_.isEmpty(type) &&
              type.id
            ) {
              var typeType = "";
              var edgeWhere = {
                name: true,
                where: {
                  is_template: 1,
                  type: type.id,
                },
              };
              switch (options.where.group_type) {
                case "Project":
                  typeType = "groups";
                  edgeWhere.where.group_type = "Project";
                  break;
              }

              modal.body.makeNode("template", "div", {});

              sb.notify({
                type: "view-field",
                data: {
                  ui: modal.body.template,
                  type: "edge",
                  property: "template",
                  obj: type,
                  options: {
                    commitUpdates: false,
                    editing: true,
                    multi: false,
                    objectType: typeType,
                    where: edgeWhere,
                    canCreate: false,
                  },
                },
              });
            }

            modal.body.patch();

            var objType =
              Blueprint[getTypeField(options.fields).field].objectType;
            modal.footer
              .makeNode("save", "div", {
                tag: "button",
                text: "Save",
                css: "ui center aligned primary button",
              })
              .notify(
                "click",
                {
                  type: "collections-run",
                  data: {
                    run: saveNewWorkflow,
                  },
                },
                sb.moduleId
              );

            $(modal.body.form.name.selector).keydown(function (ev) {
              if (ev.keyCode == 13) {
                saveNewWorkflow();
              }
            });

            modal.footer.patch();

            modal.show();
          }

          function refreshTypes(onlyUpdateTypesUi) {
            types_ui(ui, options);
            ui.types.menu.patch();

            if (onlyUpdateTypesUi || !_.isEmpty(EntityType)) {
              return;
            }

            var txt = "All types";
            if (_.findWhere(Types, { id: Query.type })) {
              txt = _.findWhere(Types, { id: Query.type }).name;
            }

            ui.types.dropdown("set text", txt);

            var groupBy = undefined;
            if (options.groupBy && options.groupBy.field) {
              groupBy = options.groupBy.field;
            }

            setGroupBy(options, groupBy);
            refresh_pool(View.Body, options);
          }

          function setSelectedType() {}

          function toggleDefaultType(type, types, onComplete) {
            var updates = [];
            if (type.is_default_type) {
              updates = [
                {
                  id: type.id,
                  is_default_type: 0,
                },
              ];
            } else {
              updates = [
                {
                  id: type.id,
                  is_default_type: 1,
                },
              ];

              var currentDefaults = _.where(types, { is_default_type: 1 });
              if (currentDefaults) {
                updates = _.map(currentDefaults, function (defaultToUpd) {
                  return {
                    id: defaultToUpd.id,
                    is_default_type: 0,
                  };
                });

                updates.push({
                  id: type.id,
                  is_default_type: 1,
                });
              }
            }

            sb.data.db.obj.update(
              type.object_bp_type,
              updates,
              function (response) {
                if (Array.isArray(response)) {
                  _.each(response, function (updated) {
                    _.findWhere(types, { id: updated.id }).is_default_type =
                      updated.is_default_type;
                  });
                }

                // update types
                onComplete();
              }
            );
          }

          var defaultTxt = "All types";
          var typeTxt = "workflow";
          var showWorkflowsLink = true;
          var canEdit = true;
          if (state.typeFilter) {
            defaultTxt = _.findWhere(Types, { id: state.typeFilter }).name;
          }

          if (
            _.isEmpty(Types) &&
            (typeof options.editSubType !== "function" ||
              !options.editBlueprint ||
              // Or if this is a subset
              (typeof options.objectType === "string" &&
                options.objectType.includes(".")))
          ) {
            return;
          }

          if (!EntityType) {
            if (currentType && currentType.hasOwnProperty("id")) {
              defaultTxt = _.findWhere(Types, { id: currentType.id }).name;
              Query.type = currentType.id;

              setGroupBy(options, options.groupBy.field);

              /*
	if(!currentState.hasOwnProperty('id')) {

								setGroupBy(options, options.groupBy.field);
								refresh_pool (View.Body, options);

							}
	*/
            } else {
              if (
                options.onBoxview !== true &&
                options.hasOwnProperty("config") &&
                options.config.hasOwnProperty("subviews") &&
                options.config.subviews.hasOwnProperty(ViewType.name) &&
                options.config.subviews[ViewType.name].hasOwnProperty("groupBy")
              ) {
                currentType = _.findWhere(Types, {
                  id: parseInt(
                    options.config.subviews[ViewType.name].groupBy.id
                  ),
                });

                if (currentType) {
                  Query.type = currentType.id;
                  defaultTxt = currentType.name;
                }

                setGroupBy(options, options.groupBy.field);
              } else {
                delete Query.type;

                defaultTxt = "All types";

                setGroupBy(options, options.groupBy.field);
              }
            }
          } else {
            typeTxt = "subset";
            showWorkflowsLink = false;
            canEdit = options.editBlueprint;
          }

          ui.makeNode("types", "div", {
            text:
              '<i class="caret square down icon"></i><span class="text">' +
              defaultTxt +
              '</span><i class="dropdown icon"></i>',
            css: "ui dropdown transparent button",
            listener: {
              onChange: function (value, text) {
                currentType = _.findWhere(Types, { id: parseInt(value) });

                if (value && parseInt(value) != Query.type) {
                  if (value === "new") {
                    ui.types.dropdown("restore defaults");
                    ui.types.dropdown("set selected", Query.type);

                    editTypeModal(
                      View["large-modal"],
                      Types,
                      function (response) {
                        Types.push(response);
                        Query.type = response.id;

                        //refreshTypes();
                      }
                    );

                    return;
                  } else if (value.split("-").length > 1) {
                    ui.types.dropdown("restore defaults");
                    ui.types.dropdown("set selected", Query.type);

                    var action = value.split("-")[0];
                    var objId = parseInt(value.split("-")[1]);

                    var typeObj = _.findWhere(Types, { id: objId });

                    switch (action) {
                      case "edit":
                        editTypeModal(
                          View["large-modal"],
                          Types,
                          function (response) {
                            Types = _.filter(Types, function (type) {
                              return type.id !== response.id;
                            });
                            Types.push(response);
                            Query.type = response.id;

                            refreshTypes();
                          },
                          typeObj
                        );

                        break;

                      case "toggleDefault":
                        toggleDefaultType(typeObj, Types, function () {
                          refreshTypes(true);
                        });
                        break;

                      case "rm":
                        sb.dom.alerts.ask(
                          {
                            title: "Remove '" + typeObj.name + "'?",
                            text: "",
                          },
                          function (response) {
                            if (response) {
                              swal.disableButtons();

                              sb.data.db.obj.erase(
                                typeObj.object_bp_type,
                                typeObj.id,
                                function () {
                                  swal.close();
                                  Types = _.filter(Types, function (type) {
                                    return type.id !== typeObj.id;
                                  });
                                  // 															Query.type = 'all';
                                  refreshTypes();
                                }
                              );
                            } else {
                              swal.close();
                            }
                          }
                        );
                        break;
                    }
                  } else {
                    var groupBy = undefined;
                    if (
                      options.groupBy &&
                      options.groupBy.field &&
                      !EntityType
                    ) {
                      groupBy = options.groupBy.field;
                    }
                    Query.type = parseInt(value);

                    if (Query.type === 0) {
                      Query.state = 0;
                      currentState = {};

                      currentRole = {};
                    }

                    setGroupBy(options, groupBy);
                    refresh_pool(View.Body, options);
                  }

                  State.typeFilter = Query.type;
                }

                states_ui(ui, options);
              },
              type: "dropdown",
              // 							placeholder:'All types',
              allowCategorySelection: true,
            },
          });

          ui.types.makeNode("menu", "div", {
            css: "menu",
          });

          // all types
          ui.types.menu
            .makeNode("all", "div", {
              css: "item",
              dataAttr: [
                {
                  name: "value",
                  value: 0,
                },
              ],
            })
            .notify(
              "click",
              {
                type: [sb.moduleId + "-run"],
                data: {
                  run: function (data) {
                    if (options.onBoxview !== true) {
                      delete options.config.subviews[ViewType.name].groupBy;

                      // !CACHING - caching types per subview
                      update_browserCache(options, function (data, callback) {
                        data.subviews = options.config.subviews;

                        callback(data);
                      });
                    }
                  },
                },
              },
              sb.moduleId
            );

          ui.types.menu.all.makeNode("txt", "div", {
            css: "text",
            tag: "span",
            text: "All types",
          });

          _.chain(Types)
            .sortBy("name")
            .each(function (type) {
              var toggleDefaultDisp = {
                msg: "Set as default workflow",
                icon: "grey star outline",
              };

              if (type.is_default_type) {
                toggleDefaultDisp.msg = "Unset as default workflow";
                toggleDefaultDisp.icon = "yellow star";
              }

              ui.types.menu
                .makeNode("t-" + type.id, "div", {
                  css: "item",
                  dataAttr: [
                    {
                      name: "value",
                      value: type.id,
                    },
                  ],
                })
                .notify(
                  "click",
                  {
                    type: [sb.moduleId + "-run"],
                    data: {
                      run: function (data) {
                        if (
                          options.onBoxview !== true &&
                          options.config.hasOwnProperty("subviews") &&
                          options.config.subviews.hasOwnProperty(
                            ViewType.name
                          ) &&
                          options.config.subviews[ViewType.name].hasOwnProperty(
                            "groupBy"
                          )
                        ) {
                          if (
                            options.config.subviews[ViewType.name].groupBy
                              .id !== type.id
                          ) {
                            delete options.config.subviews[ViewType.name]
                              .groupBy.groups;
                          }

                          options.config.subviews[ViewType.name].groupBy.id =
                            type.id;
                        } else {
                          if (options.onBoxview !== true) {
                            options.config.subviews[ViewType.name].groupBy = {
                              id: type.id,
                            };
                          }
                        }

                        if (options.onBoxview !== true) {
                          // !CACHING - caching types per subview
                          update_browserCache(
                            options,
                            function (data, callback) {
                              data.subviews = options.config.subviews;

                              callback(data);
                            }
                          );
                        }
                      },
                    },
                  },
                  sb.moduleId
                );

              ui.types.menu["t-" + type.id].makeNode("txt", "div", {
                css: "text",
                tag: "span",
                text: type.name,
              });

              if (!canEdit) {
                return;
              }

              // Create menu
              ui.types.menu["t-" + type.id].makeNode("menu", "div", {
                css: "menu",
              });

              // Edit
              var editSetup = {
                css: "item",
              };
              if (typeof options.editSubType !== "function") {
                editSetup.href = sb.data.url.createPageURL("object", {
                  id: type.id,
                  name: type.name,
                  object_bp_type: "entity_workflow",
                  type: "entity_workflow",
                });
                editSetup.tag = "a";
              } else {
                editSetup.dataAttr = [
                  {
                    name: "value",
                    value: "edit-" + type.id,
                  },
                ];
              }
              ui.types.menu["t-" + type.id].menu
                .makeNode("edit", "div", editSetup)
                .makeNode("txt", "div", {
                  css: "text",
                  tag: "span",
                  text: '<i class="yellow edit icon"></i> Edit ' + typeTxt,
                });

              if (typeTxt !== "subset") {
                // Set default
                ui.types.menu["t-" + type.id].menu
                  .makeNode("toggleDefault", "div", {
                    css: "item",
                    dataAttr: [
                      {
                        name: "value",
                        value: "toggleDefault-" + type.id,
                      },
                    ],
                  })
                  .makeNode("txt", "div", {
                    css: "text",
                    tag: "span",
                    text:
                      '<i class="' +
                      toggleDefaultDisp.icon +
                      ' icon"></i> ' +
                      toggleDefaultDisp.msg,
                  });
              }

              // Remove
              ui.types.menu["t-" + type.id].menu
                .makeNode("rm", "div", {
                  css: "item",
                  dataAttr: [
                    {
                      name: "value",
                      value: "rm-" + type.id,
                    },
                  ],
                })
                .makeNode("txt", "div", {
                  css: "text",
                  tag: "span",
                  text: '<i class="red remove icon"></i> Remove ' + typeTxt,
                });
            });

          // create new type
          if (canEdit) {
            ui.types.menu
              .makeNode("new", "div", {
                css: "item",
                dataAttr: [
                  {
                    name: "value",
                    value: "new",
                  },
                ],
              })
              .makeNode("txt", "div", {
                css: "text",
                tag: "span",
                text: '<i class="green plus icon"></i> New ' + typeTxt,
              });
          }

          // open types/workflows collection in new page
          if (showWorkflowsLink) {
            ui.types.menu
              .makeNode("open", "div", {
                css: "item",
                tag: "a",
                href: sb.data.url.createPageURL("custom", {
                  name: "Workflows",
                  id: "workflows",
                  params: {
                    t: find_type_type(options, Blueprint),
                  },
                }),
              })
              .makeNode("txt", "div", {
                css: "text",
                tag: "span",
                text: '<i class="teal share icon"></i> View all workflows',
              });
          }
        }

        function roles_ui(ui, options) {
          var defaultTxt = "All roles";

          var Roles = [
            { id: "admin", name: "Admin" },
            { id: "reviewer", name: "Reviewer" },
            { id: "specialist", name: "Specialist" },
            { id: "technician", name: "Technician" },
            { id: "irs_liaison", name: "IRS Liaison" },
          ];

          if (_.isEmpty(Types) || EntityType) {
            return;
          }

          if (currentRole !== undefined && currentRole.hasOwnProperty("id")) {
            defaultTxt = _.findWhere(Roles, { id: currentRole.id }).name;

            $("#defaultText").html(defaultTxt);

            Query.role = currentRole.id;
            setGroupBy(options, options.groupBy.field);
            refresh_pool(View.Body, options);
          } else {
            defaultTxt = "All roles";

            $("#defaultRoleText").html(defaultTxt);
          }

          ui.makeNode("roles", "div", {
            text:
              '<i class="caret square down icon"></i><span id="defaultRoleText" class="text">' +
              defaultTxt +
              '</span><i class="dropdown icon"></i>',
            css: "ui dropdown transparent button",
            listener: {
              onChange: function (value, text) {
                if (value != Query.role) {
                  var groupBy = undefined;

                  if (options.groupBy && options.groupBy.field) {
                    groupBy = options.groupBy.field;
                  }

                  Query.role = value;

                  currentRole = _.findWhere(Roles, { id: value });

                  setGroupBy(options, groupBy);
                  refresh_pool(View.Body, options);

                  //Category.typeFilter = Query.role;
                }
              },
              type: "dropdown",
              //							placeholder:'All types',
              allowCategorySelection: true,
            },
          });

          ui.roles.makeNode("menu", "div", {
            css: "menu",
          });

          // all types
          ui.roles.menu
            .makeNode("all", "div", {
              css: "item",
              dataAttr: [
                {
                  name: "value",
                  value: 0,
                },
              ],
            })
            .makeNode("txt", "div", {
              css: "text",
              tag: "span",
              text: "All roles",
            });

          if (currentRole !== undefined) {
            _.chain(Roles)
              .sortBy("uid")
              .each(function (wf_role) {
                ui.roles.menu
                  .makeNode("t-" + wf_role.id, "div", {
                    css: "item",
                    dataAttr: [
                      {
                        name: "value",
                        value: wf_role.id,
                      },
                    ],
                  })
                  .makeNode("txt", "div", {
                    css: "text",
                    tag: "span",
                    text: wf_role.name,
                  });
              });
          }

          ui.roles.menu.patch();
        }

        function states_ui(ui, options) {
          if (
            options.advancedFilters &&
            options.advancedFilters.disabledStates
          ) {
            return true;
          }

          var defaultTxt = "All states";

          if (_.isEmpty(Types) || EntityType) {
            return;
          }

          if (
            currentType !== undefined &&
            currentType.hasOwnProperty("id") &&
            currentState.hasOwnProperty("id")
          ) {
            defaultTxt = _.findWhere(currentType.states, {
              id: currentState.id,
            }).name;

            $("#defaultText").html(defaultTxt);

            Query.state = currentState.id;
            setGroupBy(options, options.groupBy.field);
            refresh_pool(View.Body, options);
          } else {
            defaultTxt = "All states";

            $("#defaultText").html(defaultTxt);
          }

          ui.makeNode("states", "div", {
            text:
              '<i class="caret square down icon"></i><span id="defaultText" class="text">' +
              defaultTxt +
              '</span><i class="dropdown icon"></i>',
            css: "ui dropdown transparent button",
            listener: {
              onChange: function (value, text) {
                if (value && parseInt(value) != Query.state) {
                  var groupBy = undefined;

                  if (options.groupBy && options.groupBy.field) {
                    groupBy = options.groupBy.field;
                  }

                  Query.state = parseInt(value);

                  currentState = _.findWhere(currentType.states, {
                    id: parseInt(value),
                  });

                  setGroupBy(options, groupBy);
                  refresh_pool(View.Body, options);

                  State.typeFilter = Query.state;
                }
              },
              type: "dropdown",
              //							placeholder:'All types',
              allowCategorySelection: true,
            },
          });

          ui.states.makeNode("menu", "div", {
            css: "menu",
          });

          // all types
          ui.states.menu
            .makeNode("all", "div", {
              css: "item",
              dataAttr: [
                {
                  name: "value",
                  value: 0,
                },
              ],
            })
            .makeNode("txt", "div", {
              css: "text",
              tag: "span",
              text: "All states",
            });

          if (currentType !== undefined) {
            _.chain(currentType.states)
              .sortBy("uid")
              .each(function (wf_state) {
                ui.states.menu
                  .makeNode("t-" + wf_state.id, "div", {
                    css: "item",
                    dataAttr: [
                      {
                        name: "value",
                        value: wf_state.uid,
                      },
                    ],
                  })
                  .makeNode("txt", "div", {
                    css: "text",
                    tag: "span",
                    text: wf_state.name,
                  });
              });
          }

          ui.states.menu.patch();
        }

        function time_range_ui(ui, options) {

          // 					var timeRangeOptionsReports = [
          var timeRangeOptions = [
            {
              name: "Today",
              value: "today",
              selected: false,
            },
            {
              name: "This week",
              value: "this_week",
              selected: false,
            },
            {
              name: "Last 30 Days",
              value: "last_30",
              selected: false,
            },
            {
              name: "This month",
              value: "this_month",
              selected: false,
            },
            {
              name: "This quarter",
              value: "this_quarter",
              selected: false,
            },
            {
              name: "This year",
              value: "this_year",
              selected: false,
            },
            {
              name: "Last month",
              value: "last_month",
              selected: false,
            },
            {
              name: "Last quarter",
              value: "last_quarter",
              selected: false,
            },
            {
              name: "Last year",
              value: "last_year",
              selected: false,
            },
            {
              name: "Range",
              value: "range",
              selected: false,
            },
          ];


          if(options && options.subviews && options.subviews.list && options.subviews.list.range && options.subviews.list.range.includedAll){
            timeRangeOptions.unshift({
              name: "All time",
              value: 'all_time',
              selected: false
            });
          }

          function convertTimeZone(date, fromTimeZone, outputFormat = 'YYYY-MM-DD HH:mm:ss') {
            const from = moment.tz(date, fromTimeZone).local();
            return from.utc(true).format(outputFormat);
          }

          function date_range_filter_ui(ui) {
            function updateRange(type, value) {
              if (type == "end") {
                var endOfDate = moment(value).endOf('day');
                var tz = moment.tz.guess();
                var newTime = convertTimeZone(endOfDate, tz);
                Query.range["end"] =  moment(newTime, 'YYYY-MM-DD HH:mm:ss').subtract(1, 'd').local(true);
              } else if (type == "start") {
                var startOfDate = moment(value).startOf('day');
                var tzs = moment.tz.guess();
                var newTime = convertTimeZone(startOfDate, tzs);
                Query.range["start"] =  moment(newTime, 'YYYY-MM-DD HH:mm:ss').local(true);
                //Query.range["start"] = moment(value).local();
              }
              state.range = Query.range;
              refresh_pool(View.Body, options);
            }

            ui.makeNode("start", "div", {
              text:
                '<div class="field">' +
                '<div class="ui calendar" id="rangestart">' +
                '<div class="ui input left icon">' +
                '<i class="calendar icon"></i>' +
                '<input type="text" placeholder="Start" value="' +
                moment().startOf("month").format("MM/DD/YYYY") +
                '">' +
                "</div>" +
                "</div>" +
                "</div>",
              style: "display:inline-block;",
            });

            ui.makeNode("to", "div", {
              text: '<div class="field">' + " to " + "</div>",
              style: "display:inline-block;",
            });

            ui.makeNode("end", "div", {
              text:
                '<div class="field">' +
                '<div class="ui calendar" id="rangeend">' +
                '<div class="ui input left icon">' +
                '<i class="calendar icon"></i>' +
                '<input type="text" placeholder="End" value="' +
                moment().endOf("month").format("MM/DD/YYYY") +
                '">' +
                "</div>" +
                "</div>" +
                "</div>",
              style: "display:inline-block;",
            }).listeners.push(function (selector) {
              $("#rangestart").calendar({
                type: "date",
                endCalendar: $("#rangeend"),
                onChange: function (type, date, text, mode) {
                  updateRange(type, date);
                }.bind({}, "start"),
              });
              $("#rangeend").calendar({
                type: "date",
                startCalendar: $("#rangestart"),
                onChange: function (type, date, text, mode) {
                  updateRange(type, date);
                }.bind({}, "end"),
              });
            });
          }

          var selectedRange = "";
          if (Query.range) {
            selectedRange = Query.range;

            if (selectedRange === "range") {
              Query.range = {
                start: moment().startOf("month"),
                end: moment().endOf("month"),
              };
              state.range = Query.range;
            } else if (
              typeof Query.range === "object" &&
              Query.range.hasOwnProperty("start")
            ) {
              selectedRange = "range";
            }
          }

          //!WORKING HERE: FIX THIS
          state.range = Query.range;
          var placeholder = '';
          if (_.findWhere(timeRangeOptions, { value: selectedRange })) {
            placeholder = _.findWhere(timeRangeOptions, {
              value: selectedRange,
            }).name;
          }

          if (_.findWhere(timeRangeOptions, { value: selectedRange })) {
            _.findWhere(timeRangeOptions, {
              value: selectedRange,
            }).selected = true;
          }

          ui.makeNode("time_range", "div", {
            text:
              '<i class="clock icon"></i><span class="text">' +
              placeholder +
              '</span><i class="dropdown icon"></i>',
            css: "ui dropdown transparent button",
            listener: {
              onChange: function (value, text, choice) {
                // Added this block to prevent 'onChange' from running on page startup
                if (
                  (text === null || value === text.toLowerCase() && value != 'today') &&
                  value != "range"
                ) {
                  return;
                }

                if (!_.isEmpty(value) && value !== Query.range) {
                  // update subview specific defaults based on collection options
                  if (options.subviews && options.subviews[ViewType.name]) {
                    if (
                      options.subviews[ViewType.name].range &&
                      _.contains(
                        options.subviews[ViewType.name].range.not,
                        value
                      )
                    ) {
                      return false;
                    }
                  }

                  if (value === "range") {
                    Query.range = {
                      start: moment().startOf("month"),
                      end: moment().endOf("month"),
                    };
                    date_range_filter_ui(ui.range);
                    ui.range.patch();
                  } else {
                    ui.range.empty();
                    ui.range.patch();
                    Query.range = value;
                  }

                  state.range = Query.range;

                  refresh_pool(View.Body, options);
                }
              },
              type: "dropdown",
              values: timeRangeOptions,
              placeholder: placeholder,
            },
          });

          ui.makeNode("range", "div", {});

          if (selectedRange === "range") {
            date_range_filter_ui(ui.range);
          }
        }

        function subMenu_filter_ui(ui, options) {
          function getFilterOptions(filter, filterKey, callback) {
            if (typeof filter.getOptions === "function") {
              filter.getOptions(callback);
            } else {
              var targetObjs = [];
              var selectOptionsIds = [];
              var selectOptions = [];

              if (!_.isEmpty(Data)) {
                targetObjs = _.chain(Data).pluck(filterKey).compact().value();

                selectOptionsIds = _.pluck(targetObjs, "id");
                selectOptions = _.filter(targetObjs, function (o) {
                  if (!_.isNull(o) && _.contains(selectOptionsIds, o.id)) {
                    selectOptionsIds = _.without(selectOptionsIds, o.id);
                    return o;
                  }
                });

                if (filter.type === "user") {
                  selectOptions.push({
                    id: "unassaigned",
                    name: "Unassigned",
                  });
                }

                callback(selectOptions);
              }
            }
          }

          function showFilter(ui, filter, filterKey, options) {
            var defaultTxt = "";
            var defaultVal = "";

            if (typeof filter === "string") {
              defaultTxt = "All " + filter;
            } else if (typeof filter === "object") {
              if (filter.defaultText) {
                defaultTxt = filter.defaultText;
              } else {
                defaultTxt = "All " + filter.title;
              }

              if (filter.defaultValue) {
                defaultVal = filter.defaultValue;
              }
            }

            filterSelections[filterKey] = defaultVal;

            ui.makeNode("f-" + filterKey, "div", {
              text:
                '<input type="hidden" value="' +
                defaultVal +
                '">' +
                '<span id="defaultText" class="default text">' +
                defaultTxt +
                "</span>" +
                '<i class="dropdown icon"></i>',
              css: "ui dropdown transparent button",
              listener: {
                onChange: function (value, text) {
                  filterSelections[this.title] = value;

                  if (typeof filter.parseSelection === "function") {
                    filter.parseSelection(value, options);
                  } else {
                    var groupBy = undefined;

                    if (options.groupBy && options.groupBy.field) {
                      groupBy = options.groupBy.field;
                    }

                    if (parseInt(value) !== 0) {
                      Query.filters.push({
                        field: "owner",
                        type: "equal_to",
                        value: parseInt(value),
                      });
                    } else {
                      Query.filters = [];
                    }
                  }

                  setGroupBy(options, groupBy);
                  refresh_pool(View.Body, options);
                }.bind(filter),
                type: "dropdown",
                allowCategorySelection: true,
              },
            });

            getFilterOptions(
              filter,
              filterKey,
              function (selectOptions, shouldPatch) {
                ui["f-" + filterKey].makeNode("menu", "div", {
                  css: "menu",
                });

                if (!options.filterBy[filterKey].hideDefaultValue) {
                  // all types
                  ui["f-" + filterKey].menu
                    .makeNode("all", "div", {
                      css: "item",
                      dataAttr: [
                        {
                          name: "value",
                          value: 0,
                        },
                      ],
                    })
                    .makeNode("txt", "div", {
                      css: "text",
                      tag: "span",
                      text: defaultTxt,
                    });
                }

                _.each(selectOptions, function (obj) {
                  if (obj !== false && obj != undefined) {
                    var data = [
                      {
                        name: "value",
                        value: obj.id,
                      },
                    ];
                    var img = "";

                    if (filter.type === "user") {
                      img += filter.title + ": ";

                      if (
                        obj.profile_image != null &&
                        obj.profile_image.hasOwnProperty("loc") &&
                        obj.profile_image.loc !== "//"
                      ) {
                        img +=
                          '<img style="padding:0;height:14px;width:14px;" class="ui cirular avatar image" src="' +
                          sb.data.files.getURL(obj.profile_image) +
                          '">';
                      } else if (obj.id === "unassaigned") {
                        img +=
                          '<i style="padding:0;" class="user circle outline icon"></i>';
                      } else {
                        img +=
                          '<i style="padding:0;" class="user circle icon"></i>';
                      }
                    }

                    ui["f-" + filterKey].menu
                      .makeNode("item-" + obj.id, "div", {
                        css: "item",
                        dataAttr: data,
                      })
                      .makeNode("text", "div", {
                        tag: "span",
                        text: img + obj.name,
                      });
                  }
                });

                // When fetching options asynchronously.
                if (shouldPatch) {
                  ui.patch();
                }
              }
            );
          }

          if (
            typeof options.filterBy === "object" &&
            !_.isEmpty(options.filterBy)
          ) {
            _.each(options.filterBy, function (filter, filterKey) {
              showFilter(ui, filter, filterKey, options);
            });
          } else {
            return;
          }
        }

        if (options.search !== false) {
          search_ui(ui, options);
        }

        if (options.config !== undefined) {
          if (options.config.hasOwnProperty("type")) {
            currentType = _.findWhere(Types, {
              id: parseInt(options.config.type),
            });
          }

          if (options.config.hasOwnProperty("state")) {
            var typeStates = _.findWhere(Types, {
              id: parseInt(options.config.type),
            }).states;

            currentState = _.findWhere(typeStates, {
              id: parseInt(options.config.state),
            });
          }
        }

        if (options.filters !== false) {
          types_ui(ui, options);

          states_ui(ui, options);

          if (options.advancedFilters) {
            if (options.advancedFilters.role) {
              roles_ui(ui, options);
            }
          }

          if (options.filterBy !== undefined) {
            subMenu_filter_ui(ui, options);
          }

          // Date range selection
          if (
            !ViewType.getsDataByRange &&
            !ViewType.hideTimeRangeFilter &&
            options.hideDateRange !== true
          ) {
            time_range_ui(ui, options);
          }

          // BLOCK: (in progress) Selection of date field to query the range against
          if (ViewType.getsDataByRange && options.entityType) {
            date_range_prop_ui(ui, options);
          }
        } else {
          if (options.showDateRange === true) {
            time_range_ui(ui, options);
          }
        }
      }

      //!TODO: save state of each subview

      var container = ui;
      var css = "ui secondary tiny top attached menu";
      if (!_.isEmpty(options.menu) && options.menu.css) {
        css = options.menu.css;
      }

      ui.makeNode("searchAboveCollection", "div", {});

      if (options.menu !== false) {
        menu_ui(
          ui.makeNode("menu", "div", {
            // 						css: 'ui inverted blue mini top attached menu'
            id: "collectionsHeader",
            css: css,
            style: "background-color: white !important;",
            //						, style: 'background-color:#2185D0;'
          }),
          options,
          State
        );
      }

      if (options.submenu !== false) {
        submenu_ui(
          ui.makeNode("submenu", "div", {
            style: "min-height:40px;",
          }),
          options,
          State
        );
      } else if (options.searchAboveCollection !== false) {
        search_ui(ui.searchAboveCollection, options);
      }
    }

    function pool_ui(ui, Query, pools) {
      var hq = undefined;
      var tagList = pools.sort(function (a, b) {
        function getTagTxt(tag) {
          switch (tag.object_bp_type) {
            case "groups":
              return tag.name;

            case "users":
              return tag.fname + " " + tag.lname;

            case "system_tags":
              return tag.tag;
          }
        }

        if (a.object_bp_type === b.object_bp_type) {
          return getTagTxt(a)
            .toLowerCase()
            .localeCompare(getTagTxt(b).toLowerCase());
        } else {
          if (
            (a.object_bp_type == "groups" && b.object_bp_type == "users") ||
            (a.object_bp_type == "groups" &&
              b.object_bp_type == "system_tags") ||
            (a.object_bp_type == "users" && b.object_bp_type == "system_tags")
          ) {
            return -1;
          } else {
            return 1;
          }
        }
      });

      function toggle_pool(Query, pool, ui) {
        Query.tagged_with = _.filter(Query.tagged_with, function (tag) {
          return tag > 0;
        });

        if (_.isObject(pool)) {
          if (!Query.tagged_with) {
            if (pool.selectedTags) {
              Query.tagged_with = pool.selectedTags;
            } else {
              Query.tagged_with = _.pluck(pool, "id");
            }
          } else {
            if (pool.selectedTags) {
              _.each(pool.selectedTags, function (tag) {
                Query.tagged_with.push(tag);
              });
            } else {
              _.each(pool, function (tag) {
                Query.tagged_with.push(tag.id);
              });
            }
          }

          if (pool.deselectedTags) {
            Query.tagged_with = _.difference(
              Query.tagged_with,
              pool.deselectedTags
            );
          } else {
            Query.tagged_with = _.difference(
              Query.tagged_with,
              _.pluck(pool, "id")
            );
          }
        } else {
          if (!Query.tagged_with) {
            Query.tagged_with = [pool.id];
          } else if (
            Query.tagged_with &&
            _.contains(Query.tagged_with, pool.id)
          ) {
            var filtered = _.reject(Query.tagged_with, function (p) {
              return p === pool.id;
            });

            Query.tagged_with = filtered;
          } else {
            Query.tagged_with.push(pool.id);
          }
        }

        return true;
      }

      function show_pool(ui, taglist) {
        _.each(tagList, function (pool, i) {
          var tagUI = tag_ui(ui, pool, {
            size: "small",
          });

          tagUI.notify(
            "click",
            {
              type: "collections-run",
              data: {
                run: function (pool) {
                  if (toggle_pool(Query, pool, this)) {
                    refresh_pool(View.Body, options);
                  }

                  if (!pool.hasOwnProperty("isSelected")) {
                    pool.isSelected = true;
                  } else {
                    if (pool.isSelected) {
                      pool.isSelected = false;
                    } else {
                      pool.isSelected = true;
                    }
                  }
                }.bind(tagUI, pool),
              },
            },
            sb.moduleId
          );
        });

        ui.makeNode("less", "div", {
          css: "ui small label show-pointer",
          text: "show less...",
        }).notify(
          "click",
          {
            type: [sb.moduleId + "-run"],
            data: {
              run: function (data) {
                ui.empty();

                hide_pool(ui, taglist);

                ui.patch();
              },
            },
          },
          sb.moduleId
        );

        ui.patch();
      }

      function hide_pool(ui, taglist) {
        function build_tags() {
          _.each(tagList, function (pool, i) {
            if (pool.isSelected) {
              var tagUI = tag_ui(ui, pool, {
                size: "small",
              });

              tagUI.notify(
                "click",
                {
                  type: "collections-run",
                  data: {
                    run: function (pool) {
                      if (toggle_pool(Query, pool, this)) {
                        refresh_pool(View.Body, options);
                      }

                      if (pool.isSelected) {
                        pool.isSelected = false;
                      } else {
                        pool.isSelected = true;
                      }
                    }.bind(tagUI, pool),
                  },
                },
                sb.moduleId
              );
            }
          });

          if (tagList.length > 2) {
            ui.makeNode("more", "div", {
              css: "ui small label show-pointer",
              text: "more tags...",
            }).notify(
              "click",
              {
                type: [sb.moduleId + "-run"],
                data: {
                  run: function (data) {
                    delete ui.more;

                    show_pool(ui, tagList);
                  },
                },
              },
              sb.moduleId
            );
          }
        }

        function fetch_hq(callback) {
          sb.data.db.obj.getWhere(
            "groups",
            {
              group_type: "Headquarters",
              childObjs: {
                name: true,
              },
            },
            function (hqArr) {
              callback(hqArr[0]);
            }
          );
        }

        if (options.layer === "hq") {
          if (hq === undefined) {
            fetch_hq(function (res) {
              hq = res;

              var hqTagUI = tag_ui(
                ui,
                {
                  name: hq.name,
                  object_bp_type: "groups",
                  layer: "hq",
                },
                {
                  size: "small",
                }
              );

              build_tags();

              ui.patch();
            });
          } else {
            var hqTagUI = tag_ui(
              ui,
              {
                name: hq.name,
                object_bp_type: "groups",
                layer: "hq",
              },
              {
                size: "small",
              }
            );

            build_tags();

            ui.patch();
          }
        } else {
          var parent = _.findWhere(pools, { id: Query.tagged_with[0] });

          var mainTagUI = tag_ui(ui, parent, {
            size: "small",
          });

          build_tags();
        }
      }

      // *************** DO NOT REMOVE YET **************

      /*
var tagList = _.filter(pools, function(pool){

				if (pool.group_type === 'Team') {

					teamTag = pool.id;
					return false;

				} else if (pool.id === options.pool) {

					teamTag = pool.id;
					return false;

				}

				return true;

			})
*/

      /*
if(options.layer !== 'hq') {

				tagList = _.filter(pools,  function(pool) {

					if(Query.tagged_with[0] !== pool.id
					&& pool.group_type === 'Team') {

						teamTag = pool.id;

						return false;

					} else if(pool.id === options.pool) {

						teamTag = pool.id;

						return false;

					}

					return true;

				});

			}
*/

      // ************************************************

      /*
if(appConfig.instance === 'rickyvoltz'
				&& appConfig.instance === 'voltzsoftware') {
*/

      ui.makeNode("tagsWrap", "div", {});

      var state = appConfig.state;
      var pageObj = appConfig.state.pageObject;

      if (options.state) {
        if (options.state.hasOwnProperty("myStuff")) {
          state = options.state;
          pageObj = options.state;
        }
      }

      var url = window.location.href.split("#")[1].split("&");
      var rootLevel = url[0];

      if (pageObj === undefined || pageObj.object_bp_type === "entity_type") {
        if (state.hasOwnProperty("project")) {
          pageObj = state.project;
        } else if (state.hasOwnProperty("team")) {
          pageObj = state.team;
        } else if (state.hasOwnProperty("headquarters")) {
          pageObj = state.headquarters;
        }
      }

      pageObj.tagged_with = [];

      if (pageObj.object_bp_type === "users") {
        pageObj.tagged_with.push(pageObj.id);
      } else {
        if (pageObj.hasOwnProperty("group_type")) {
          if (pageObj.group_type !== "Headquarters") {
            pageObj.tagged_with.push(pageObj.id);
          } else {
            if (rootLevel === "mystuff" && url.length < 3) {
              pageObj.tagged_with.push(+sb.data.cookie.userId);
            } else {
              pageObj.tagged_with.push(pageObj.id);
            }
          }
        } else {
          if (pageObj.object_bp_type === "project_types") {
            pageObj.tagged_with.push(pageObj.id);
          } else {
            pageObj.tagged_with = [];

            if (
              state.hasOwnProperty("params") &&
              !_.isEmpty(state.params) &&
              state.params.hasOwnProperty("e")
            ) {
              pageObj.tagged_with.push(state.params.e);
            } else {
              pageObj.tagged_with.push(pageObj.id);
            }
          }
        }
      }

      var tags = [];

      if (!options.ignorePageFilter) {
        tags = pageObj.tagged_with;
      }

      if (options.tags) {
        tags = tags.concat(options.tags);
      }

      var hiddenTags = [];
      if (options.hiddenTags) {
        hiddenTags = options.hiddenTags;
      }

      var selectedTags = [];
      if (options.selectedTags) {
        selectedTags = options.selectedTags;
        tags = tags.concat(options.selectedTags);
      }

      tags = _.uniq(tags);

      var allTagsAreToggleable = false;
      if (options.allTagsAreToggleable) {
        allTagsAreToggleable = true;
      }
      if (options.tagDeslection) {
        allTagsAreToggleable = true;
      }

      var minTagSelection = 1;
      if (!_.isUndefined(options.minTagSelection)) {
        minTagSelection = options.minTagSelection;
      }

      var maxTagSelection = null;
      if (options.maxTagSelection) {
        maxTagSelection = options.maxTagSelection;
      }

      var canSearchForTags = true;
      if (!_.isUndefined(options.canSearchForTags)) {
        canSearchForTags = options.canSearchForTags;
      }

      // tags
      sb.notify({
        type: "view-field",
        data: {
          type: "tags",
          property: "tagged_with",
          obj: pageObj,
          options: {
            canEdit: false,
            canSearch: canSearchForTags,
            allTagsAreToggleable: allTagsAreToggleable,
            minTagSelection: minTagSelection,
            maxTagSelection: maxTagSelection,
            tagList: tags,
            selectedTags: selectedTags,
            hiddenTags: hiddenTags,
            build: false,
            filter: {
              run: function (tags) {
                toggle_pool(Query, tags, ui);
                refresh_pool(View.Body, options);
                $(
                  ui.tagsWrap.div.btnGroup.add.selector + " > .input > input"
                ).focus();
              },
            },
          },
          ui: ui.tagsWrap,
        },
      });

      /*
} else {

				hide_pool(ui, tagList);

			}
*/
    }

    function body_ui(ui, options, data, isLoading, onDraw) {
      function metrics_ui(data, options) {
        if (!_.isEmpty(options.metrics)) {
          return function (ui) {};
        } else {
          return false;
        }
      }

      function get_child_data(obj, callback) {
        var query = _.clone(Query);
        query.where = _.clone(Query.where);

        if (Array.isArray(obj)) {
          query.where.parent = {
            type: "or",
            values: _.pluck(obj, "id"),
          };
        } else {
          query.where.parent = obj.id;
        }

        var opts = _.clone(options);
        options.where = _.clone(options.where);
        options.where.parent = obj.id;

        get_data(
          options,
          query,
          function (object_list, data) {
            callback(data);
          },
          Page,
          Types,
          ViewType
        );
      }

      function page_nav_ui(ui, Query, Data) {
        function change_page(btn, newPage) {
          // !FEATURE BLOCK -- SAVING COLLECTIONS PAGES (01-27-20)
          if (false) {
            if (
              options.onBoxview === undefined ||
              options.onBoxview === false
            ) {
              saveTo_cookie(options.config.colUID, "page", newPage);
              options.config.page = newPage;
            }
          }

          $(btn.selector).html(
            '<i class="ui grey notched circle loading icon"></i>'
          );
          Page.page = parseInt(newPage);
          refresh_pool(View.Body, options);
        }

        function change_page_length(btn, pageLength) {
          // 					btn.loading();
          options.pageLength = pageLength;
          Page.page = 1;
          refresh_pool(View.Body, options);
        }

        function applyPageBtnNotification(btn, page) {
          btn.notify(
            "click",
            {
              type: "collections-run",
              data: {
                run: change_page.bind({}, btn, page),
              },
            },
            sb.moduleId
          );
        }

        function applyPageLengthBtnNotification(btn, pageLength) {
          btn.notify(
            "click",
            {
              type: "collections-run",
              data: {
                run: change_page_length.bind({}, btn, pageLength),
              },
            },
            sb.moduleId
          );
        }

        var numPages = Math.ceil(Page.recordsTotal / Page.pageLength);
        var currentPage = Page.page || 1;
        var pageBtnCushion = 3; // number of pages directly next to current page to show

        // !FEATURE BLOCK -- SAVING COLLECTIONS PAGES (01-27-20)
        if (false) {
          if (options.config.hasOwnProperty("page")) {
            currentPage = parseInt(options.config.page);
          }
        }

        ui.empty();

        ui.makeNode("pageLength", "div", {
          text: "# of Items",
          css: "ui simple dropdown item",
        });
        ui.pageLength.makeNode("menu", "div", {
          css: "menu",
        });
        ui.pageLength.menu.makeNode("five", "div", {
          css: "item",
          tag: "a",
          text: "5",
        });
        ui.pageLength.menu.makeNode("ten", "div", {
          css: "item",
          tag: "a",
          text: "10",
        });
        ui.pageLength.menu.makeNode("twentyfive", "div", {
          css: "item",
          tag: "a",
          text: "25",
        });
        ui.pageLength.menu.makeNode("fifty", "div", {
          css: "item",
          tag: "a",
          text: "50",
        });
        ui.pageLength.menu.makeNode("seventyfive", "div", {
          css: "item",
          tag: "a",
          text: "75",
        });
        ui.pageLength.menu.makeNode("hundred", "div", {
          css: "item",
          tag: "a",
          text: "100",
        });
        ui.pageLength.menu.makeNode("twohundred", "div", {
          css: "item",
          tag: "a",
          text: "200",
        });

        applyPageLengthBtnNotification(ui.pageLength.menu.five, 5);
        applyPageLengthBtnNotification(ui.pageLength.menu.ten, 10);
        applyPageLengthBtnNotification(ui.pageLength.menu.twentyfive, 25);
        applyPageLengthBtnNotification(ui.pageLength.menu.fifty, 50);
        applyPageLengthBtnNotification(ui.pageLength.menu.seventyfive, 75);
        applyPageLengthBtnNotification(ui.pageLength.menu.hundred, 100);
        applyPageLengthBtnNotification(ui.pageLength.menu.twohundred, 200);

        // loading state
        if (isNaN(numPages)) {
          // 					ui.makeNode('loader', 'loader', {});
        } else {
          var btnsDrawn = 0;
          var btnCss;

          // buttons at start of set
          if (currentPage - pageBtnCushion > 1) {
            applyPageBtnNotification(
              ui.makeNode("btn-to-start", "div", {
                text: 1,
                css: "item",
                tag: "a",
              }),
              1
            );

            ui.makeNode("ell-to-start", "div", {
              text: "...",
              css: "disabled item",
              tag: "a",
            });
          }

          // buttons near current page
          for (
            var i = currentPage - pageBtnCushion;
            i <= currentPage + pageBtnCushion;
            i++
          ) {
            if (i > 0 && i <= numPages) {
              if (i === currentPage) {
                btnCss = "active item";
              } else {
                btnCss = "item";
              }

              applyPageBtnNotification(
                ui.makeNode("btn-" + i, "div", {
                  text: i.toString(),
                  css: btnCss,
                  tag: "a",
                }),
                i
              );
            }
          }

          // buttons at end of set
          if (currentPage + pageBtnCushion < numPages) {
            ui.makeNode("ell-to-end", "div", {
              text: "...",
              css: "disabled item",
              tag: "a",
            });

            applyPageBtnNotification(
              ui.makeNode("btn-to-end", "div", {
                text: numPages,
                css: "item",
                tag: "a",
              }),
              numPages
            );
          }
        }
      }

      function prepare_data(list, options, viewType) {
        if (subviewShouldGroup(viewType) && ViewState.isGrouping) {
          var groups = {};
          var groupDictionary = {};

          if (typeof options.groupBy.group === "function") {
            isGrouped = true;

            if (options.groupBy.options) {
              var tmp = _.groupBy(list, options.groupBy.group);

              _.each(options.groupBy.options, function (opt, key) {
                groups[key] = tmp[key];
              });

              return groups;
            } else {
              return options.groupBy.group(list);
            }
          } else if (options.groupBy.options) {
            isGrouped = true;
            groups = [];

            // When grouping by workflows, make sure values not contained
            // within the defined workflow as states (in case the workflow
            // has changed or old data that did not get it set to the
            // initial state (issue since fixed)).
            var allowedVals = _.chain(options.groupBy.options)
              .pluck("value")
              .compact()
              .value();
            var shouldRemoveNotSetGroup = false;

            _.each(options.groupBy.options, function (option) {
              groups.push({
                color: option.color,
                title: option.name,
                value: option.value,
                data: _.filter(list, function (item) {
                  if (option.isEntryPoint) {
                    shouldRemoveNotSetGroup = true;
                  }

                  var ret = item[options.groupBy.field];

                  // For workflow fields w/out vals set
                  // (This is fixed on the back-end to not
                  // allow undefined state fields, but for old
                  // data created before the fix.)
                  if (
                    option.isEntryPoint &&
                    (!ret || !_.contains(allowedVals, ret))
                  ) {
                    return true;
                  }

                  if (ret) {
                    if (typeof ret === "object" && ret.hasOwnProperty("id")) {
                      if (ret.id === 0) {
                        return option.value === 1;
                      }

                      return ret.id == option.value;
                    }

                    if (ret === 0) {
                      return option.value === 1;
                    }
                  }

                  return ret == option.value;
                }),
              });
            });

            // No longer need the not set group, since
            if (shouldRemoveNotSetGroup) {
              groups = _.filter(groups, function (group) {
                return group.value !== null;
              });
            }

            var nullCol = _.find(groups, function (group) {
              return group.value === null;
            });

            if (nullCol && _.isEmpty(nullCol.data) && groups.length > 1) {
              groups = _.filter(groups, function (group) {
                return group.value !== null;
              });
            }

            return groups;
          } else {
            isGrouped = true;
            var type;

            // if grouping by state, get the appropriate type obj
            // containing the states set
            if (
              Query.type &&
              options.groupBy.field &&
              options.fields[options.groupBy.field].type === "state"
            ) {
              type = _.findWhere(Types, { id: Query.type });
            }

            // group data for subview
            groups = _.groupBy(list, function (item) {
              var ret = item[options.groupBy.field] || "Uncategorized";

              if (typeof ret === "undefined") {
                return;
              }

              // group by state
              if (
                Query.type &&
                options.groupBy.field &&
                options.fields[options.groupBy.field].type === "state"
              ) {
                if (type.states) {
                  if (_.findWhere(type.states, { uid: ret })) {
                    return ret;
                  } else {
                    return _.findWhere(type.states, { isEntryPoint: 1 }).uid;
                  }
                }

                // group by an edge property
              } else if (
                !_.isNull(ret) &&
                typeof ret === "object" &&
                ret.hasOwnProperty("name")
              ) {
                groupDictionary[ret.name] = ret.id;
                return ret.name;

                // group by an edge-set property
              } else if (Array.isArray(ret)) {
                if (ret[0] && ret[0].hasOwnProperty("name")) {
                  groupDictionary[_.pluck(ret, "name").join(", ")] = _.pluck(
                    ret,
                    "id"
                  );
                  return _.pluck(ret, "name").join(", ");
                } else if (ret[0] && ret[0].hasOwnProperty("fname")) {
                  var txt = _.chain(ret)
                    .map(function (item) {
                      return { txt: item.fname + " " + item.lname };
                    })
                    .pluck("txt")
                    .value()
                    .join(", ");

                  var val = _.pluck(ret, "id");

                  groupDictionary[txt] = val;
                  return txt;
                } else {
                  groupDictionary["Not assigned"] = [];
                  return "Not assigned";
                }
              } else if (
                !_.isNull(ret) &&
                ret.hasOwnProperty("fname") &&
                ret.hasOwnProperty("lname")
              ) {
                ret = ret.fname + " " + ret.lname;
              }

              return ret;
            });
          }

          var ret = _.map(groups, function (group, name) {
            var columnColor = "";
            var columnTitle = name;
            var columnValue = groupDictionary[name];

            if (columnTitle === "undefined") {
              columnTitle = "Not set";
              columnValue = null;
            }

            if (
              Query.type &&
              options.groupBy.field &&
              options.fields[options.groupBy.field].type === "state"
            ) {
              var type = _.findWhere(Types, { id: Query.type });
              var state = _.findWhere(type.states, {
                uid: parseInt(columnValue),
              });

              if (state) {
                columnTitle = state.name;
                columnColor = state.color;
              }
            } else if (parseInt(name) == name && options.groupBy.options) {
              columnTitle = _.findWhere(options.groupBy.options, {
                value: parseInt(name),
              }).name;
              columnColor = _.findWhere(options.groupBy.options, {
                value: parseInt(name),
              }).color;
            } else if (options.groupBy.options) {
              columnTitle = options.groupBy.options[name].title;
              columnColor = options.groupBy.options[name].color;
            }

            var ret = {
              color: columnColor,
              title: columnTitle,
              value: columnValue,
              data: group,
            };

            if (options.groupBy && options.groupBy.field) {
              ret.field = options.groupBy.field;
            }

            return ret;
          });

          return ret;
        } else {
          return list;
        }
      }

      function createNewItem(templateObj, mergeVars) {
        var seed = getNewSeed(templateObj, mergeVars);

        sb.notify({
          type: "get-sys-modal",
          data: {
            callback: function (modal) {
              modal.show();

              options.actions.create(
                modal.body,
                seed,

                // !TODO: refactor this to a refresh_ui func
                function (response, keepOpen) {
                  if (!keepOpen) {
                    modal.hide();
                  }
                  setTimeout(function () {
                    if (Array.isArray(Data)) {
                      Data.unshift(response);
                    } else {
                      Data = [response];
                    }

                    body_ui(View.Body, options, Data);

                    View.Body.patch();
                  }, 1);
                },
                Blueprint,
                {
                  setTemplateProps: templateForm_ui,
                  options: options,
                  mergeVars: mergeVars,
                }
              );
            },
          },
        });
      }

      // Clear any fields from previous draw
      sb.notify({
        type: "clear-collections-fields",
        data: {
          collId: options._id,
        },
      });

      // only show paging btns if more than one page
      var numPages = Math.ceil(Page.recordsTotal / Page.pageLength);
      var isGrouped = false;
      var optionsMetrics = {};

      // !FEATURE BLOCK -- SAVING COLLECTIONS PAGES (01-27-20)
      if (false) {
        if (isLoading === undefined) {
          if (_.isNaN(numPages) && ViewType.name !== "calendar") {
            options.config.page = 1;

            refresh_pool(ui, options, function () {
              //saveTo_cookie(options.config.colUID, 'page', 1);
            });
          }
        }
      }

      if (options.metrics) {
        _.each(options.metrics, function (v, k) {
          optionsMetrics[k] = {
            title: v.title.toUpperCase(),
            fields: {},
          };

          _.each(v.fields, function (val, key) {
            var view = val;
            var sumCall = false;
            if (typeof val === "object") {
              view = val.view;
              sumCall = val.sum;
            }

            optionsMetrics[k].fields[key] = function (ui, data, group, onDraw) {
              var config = this;
              var opts = _.clone(options);
              opts.where = _.clone(opts.where);

              if (group && group.value && group.field) {
                opts.where[group.field] = group.value;
              }

              if (typeof config.sum === "function") {
                var queryObj = get_data_query(
                  opts,
                  Query,
                  Page,
                  Types,
                  ViewType,
                  config.fieldName
                );

                delete queryObj.paged;
                delete queryObj.childObjs;

                config.sum(
                  opts.objectType,
                  config.fieldName,
                  queryObj,
                  function (data) {
                    if (_.isObject(data)) {
                      data.object_bp_type = opts.objectType;
                      data.propertyName = config.fieldName;
                    }

                    config.view(ui, data);
                    onDraw(group);
                  },
                  opts
                );
              } else {
                get_sum(
                  opts,
                  Query,
                  function (metricData) {
                    if (_.isObject(metricData)) {
                      metricData.object_bp_type = opts.objectType;
                      metricData.propertyName = config.fieldName;
                    }

                    config.view(
                      ui,
                      metricData,
                      {
                        getData: function (callback) {
                          get_sum(
                            opts,
                            Query,
                            function (data) {
                              callback(data);
                            },
                            Page,
                            Types,
                            ViewType,
                            config.fieldName
                          );
                        },
                      },
                      data
                    );

                    if (!group) {
                      group = {
                        value: key,
                        field: key,
                      };
                    }

                    onDraw(group);
                  },
                  Page,
                  Types,
                  ViewType,
                  config.fieldName
                );
              }
            }.bind({
              fieldName: key,
              view: view,
              sum: sumCall,
            });
          });
        });
      }

      if (!options.hidePagingAbove) {
        if (options.showPaging === true || options.showPaging === undefined) {
          if (numPages > 1) {
            page_nav_ui(
              ui.makeNode("above", "div", { css: "ui tiny pagination menu" }),
              Query,
              Data
            );
          }
        }
      }

      if (typeof ViewType === "undefined") {
        return;
      }

      options.actions_ui = function (container, item, args) {
        var css = "";
        var menuCss = "menu";
        if (args !== undefined && args.css) {
          css = args.css;
        }
        if (args !== undefined && args.menuCss) {
          menuCss = args.menuCss;
        }

        var actionButtonCss = "ui mini basic circular icon simple dropdown";
        if (args !== undefined && args.actionButtonCss) {
          actionButtonCss = args.actionButtonCss;
        }

        var actionButtonStyle = "border:none;";
        if (args !== undefined && args.actionButtonStyle) {
          actionButtonStyle = args.actionButtonStyle;
        }

        var menuStyle = "";
        if (args !== undefined && args.menuStyle) menuStyle = args.menuStyle;

        if (!_.isEmpty(options.actions) && !appConfig.is_portal) {
          container.makeNode("actions", "div", { css: css });

          container.actions
            .makeNode("menu", "div", {
              css: actionButtonCss,
              text: '<i class="ellipsis horizontal centered icon" style="font-size:1.5em !important;"></i>',
              style: actionButtonStyle,
            })
            .makeNode("menu", "div", {
              css: menuCss,
              style: menuStyle,
            });

          _.each(options.actions, function (actionSetting, name) {
            if (
              actionSetting.hasOwnProperty("singleAction") &&
              !actionSetting.singleAction
            ) {
              return;
            }

            var action = {};

            if (actionSetting === false) {
              return;
            }

            switch (name) {
              // actions that act on the collection
              case "create":
                return;
                break;

              case "createFromTemplate":
                return;
                break;

              // actions that act on single items
              case "comments":
                action = {
                  icon: "comment",
                  color: "purple",
                  title: "Comments",
                };
                break;

              case "edit":
                action = {
                  icon: "pencil",
                  color: "yellow",
                  title: "Edit",
                };
                break;

              case "view":
                action = {
                  icon: "expand",
                  color: "gray",
                  title: "Open",
                };
                break;

              case "event":
                action = {
                  icon: "calendar",
                  color: "blue",
                  title: "Calendar Event",
                };
                break;

              default:
                action = actionSetting;
                break;
            }

            var menuItemSetup = {
              css: "ui " + action.color + " item",
              text: '<i class="' + action.icon + ' icon"></i> ' + action.title,
              style: "border-radius:0px;",
            };

            if (action.hasOwnProperty("href") && action.href == true) {
              menuItemSetup.tag = "a";
              menuItemSetup.href = item.link;
            }

            container.actions.menu.menu.makeNode(name, "div", menuItemSetup);

            if (!action.hasOwnProperty("href")) {
              container.actions.menu.menu[name].notify(
                "click",
                {
                  type: "collections-run",
                  data: {
                    run: function (actionType) {
                      switch (actionType) {
                        case "view":
                          sb.notify({
                            type: "get-sys-modal",
                            data: {
                              callback: function (modal) {
                                single_ui(modal, options, item, {
                                  inModal: true,
                                });
                              },
                            },
                          });
                          break;

                        case "comments":
                          comments_ui(View["large-modal"], item, options);
                          break;

                        default:
                          // custom funcs
                          if (
                            options.actions[actionType] &&
                            typeof options.actions[actionType].action ===
                              "function"
                          ) {
                            // 										View['large-modal'].body.empty();

                            switch (options.actions[actionType].domType) {
                              case "none":
                                options.actions[actionType].action(
                                  item,
                                  customView,
                                  function (shouldUpdateList) {
                                    if (shouldUpdateList) {
                                      refresh_pool(View.Body, options);
                                    }
                                  }
                                );

                                break;

                              case "navigate":
                                options.actions[actionType].action(
                                  item,
                                  customView,
                                  function (shouldUpdateList) {}
                                );

                                break;

                              default:
                                var customView = View[
                                  "large-modal"
                                ].body.makeNode("c", "div", {
                                  style: "min-height:37vh!important;",
                                });

                                View["large-modal"].body.patch();
                                View["large-modal"].show();

                                options.actions[actionType].action(
                                  item,
                                  customView,
                                  function (shouldUpdateList) {
                                    View["large-modal"].hide();

                                    if (shouldUpdateList) {
                                      refresh_pool(View.Body, options);
                                    }
                                  },
                                  options
                                );
                            }
                          }

                          break;
                      }
                    }.bind({}, name, item),
                  },
                },
                sb.moduleId
              );
            }
          });

          if (options.actions.copy !== false) {
            container.actions.menu.menu
              .makeNode("copy", "div", {
                css: "ui yellow basic fluid item",
                text: '<i class="copy outline icon"></i> Duplicate',
                tag: "a",
                style: "border-radius:0px;",
              })
              .notify(
                "click",
                {
                  type: "collections-run",
                  data: {
                    run: function (item) {

                      var copyObj = {
                        name: item.name + "-COPY",
                        object_bp_type: options.objectType,
                        is_template: 0,
                        data_source_id: item.data_source_id
                      };

                      if (options.templates === true) {
                        copyObj.is_template = 1;
                      }

                      container.actions.menu.menu.copy.loading();
                      $(container.actions.menu.menu.copy.selector).html(
                        '<i class="notched circle loading icon"></i> Duplicating'
                      );

                      if ( item.object_bp_type === 'contracts'){

                        copyObj.data_source = (parseInt(item.data_source) || 1) + 1;
                        copyObj.data_source_id = item.data_source_id || item.id;

                        // If item was created from a template, fetch the original template
                        if (item.data_source_id && item.data_source_id !== 0) {
                          sb.data.db.obj.getById('contracts', item.data_source_id, function(template) {
                            if (template) {
                              // Use template as base but preserve copyObj defaults
                              var templateCopyObj = _.extend({}, template, copyObj);

                              templateCopyObj.data_source = copyObj.data_source;
                              templateCopyObj.data_source_id = copyObj.data_source_id;
                              templateCopyObj.object_bp_type = copyObj.object_bp_type;
                              templateCopyObj.is_template = copyObj.is_template;

                              // Remove existing version suffix and add new version
                              var baseName = template.name.replace(/ ?v\d+$/, '');
                              templateCopyObj.name = baseName + " v" + copyObj.data_source;

                              // Create from template
                              sb.data.db.obj.createFromTemplate(
                                item.id,
                                function (response) {

                                  if (response) {
                                    refresh_pool(View.Body, options);
                                  }
                                },
                                0,
                                templateCopyObj
                              );
                            } else {
                              // Fallback to regular duplication if template not found
                              var baseName = item.name.replace(/ ?v\d+$/, '');
                              copyObj.name = baseName + " v" + copyObj.data_source;

                              sb.data.db.obj.createFromTemplate(
                                item.id,
                                function (response) {
                                  if (response) {
                                    refresh_pool(View.Body, options);
                                  }
                                },
                                0,
                                copyObj
                              );
                            }
                          });
                        } else {
                          // No template reference, continue normal flow
                          var baseName = item.name.replace(/ ?v\d+$/, '');
                          copyObj.name = baseName + " v" + copyObj.data_source;

                          sb.data.db.obj.createFromTemplate(
                            item.id,
                            function (response) {
                              if (response) {
                                refresh_pool(View.Body, options);
                              }
                            },
                            0,
                            copyObj
                          );
                        }
                      } else {
                        // Non-contract objects use original logic
                        sb.data.db.obj.createFromTemplate(
                          item.id,
                          function (response) {
                            if (response) {
                              refresh_pool(View.Body, options);
                            }
                          },
                          0,
                          copyObj
                        );
                      }
                    }.bind(null, item),
                  },
                },
                sb.moduleId
              );
          }

          if (options.actions.navigateTo !== false) {
            var goToLink = sb.data.url.getObjectPageParams(item, options);
            if (item.hasOwnProperty("link")) {
              goToLink = item.link;
            }
            if (
              options.singleView &&
              typeof options.singleView.link === "function"
            ) {
              goToLink = options.singleView.link(item);
            }

            // add link
            container.actions.menu.menu.makeNode("linkTo", "div", {
              css: "ui blue basic fluid item",
              text: '<i class="external square icon"></i> Go to',
              tag: "a",
              style: "border-radius:0px;",
              href: goToLink,
            });
          }

          if (
            options.actions.archive !== false ||
            options.actions.archive === undefined
          ) {
            var archiveBtnTxt = '<i class="ui black archive icon"></i> Archive';
            if (Query.archive) {
              archiveBtnTxt = '<i class="ui yellow redo icon"></i> Restore';
            }

            // add link
            container.actions.menu.menu
              .makeNode("archive", "div", {
                css: "ui basic fluid item archive-action-single-btn",
                text: archiveBtnTxt,
                tag: "a",
                style: "border-radius:0px;",
              })
              .notify(
                "click",
                {
                  type: "collections-run",
                  data: {
                    run: function (itemId) {
                      archive_selected_items(itemId, options, function () {});
                    }.bind({}, item.id),
                  },
                },
                sb.moduleId
              );
          }

          if (options.actions.comments !== false) {
            container.actions.menu.menu
              .makeNode("commentsAction", "div", {
                css: "ui basic fluid item archive-action-single-btn",
                text: '<i class="comment icon"></i> Comments',
                tag: "a",
                style: "border-radius:0px;",
              })
              .notify(
                "click",
                {
                  type: "run-method",
                  data: {
                    run: function () {
                      comments_ui(View["large-modal"], item, options);
                    },
                  },
                },
                sb.moduleId
              );
          }

          if (
            ( appConfig.instance == 'foundation_group' || appConfig.instance == 'rickyvoltz' )
            && options.actions.sharePortal !== false)
        {
            container.actions.menu.menu
              .makeNode("sharePortal", "div", {
                css: "ui basic fluid item archive-action-single-btn",
                text: '<i class="share alternate icon"></i> Share in Portal',
                tag: "a",
                style: "border-radius:0px;",
              })
              .notify(
                "click",
                {
                  type: "run-method",
                  data: {
                    run: function (itemId) {
                      if (typeof options.actions.sharePortal.ui == "function") {
                        options.actions.sharePortal.ui(
                          View["large-modal"],
                          item,
                          options
                        );
                      }
                      // comments_ui(View['large-modal'], item, options)
                    }.bind({}, item.id),
                  },
                },
                sb.moduleId
              );
          }
        }
      };

      fieldsForSubview = {};

      ViewState.onMobile = OnMobile;

      _.each(options.fields, function (field, name) {
        if (field.type === "parent") {
          ViewState.parent = name;
          return;
        }

        if (!_.contains(ViewState.hiddenFields, name)) {
          fieldsForSubview[name] = {
            title: field.title,
            name: name,
            isPrimary: field.isPrimary,
          };

          if (field.link) {
            fieldsForSubview[name].link = field.link;
          }

          if (name === Page.sortCol) {
            fieldsForSubview[name].sort = Page.sortDir;
          }

          if (Blueprint[name]) {
            if (Blueprint.hasOwnProperty(name)) {
              fieldsForSubview[name].type = Blueprint[name].type;

              switch (Blueprint[name].type) {
                /*
case 'date':
								fieldsForSubview[name].view = function(c, obj){

									if(obj[name]){

										c.makeNode('t', 'div', {
											css:'date',
											text: moment(obj[name], 'YYYY-MM-DD HH:mm:ss').format('l')
										});

									}

								};
								break;
*/

                case "objectId":
                case "select":
                  // default child obj view
                  if (
                    Blueprint[name].objectType &&
                    Blueprint[name].selectDisplay
                  ) {
                    fieldsForSubview[name].view = function (c, obj) {
                      if (obj[name]) {
                        c.makeNode("t", "div", {
                          tag: "a",
                          text: '<i class="linkify icon"></i>' + obj[name].name,
                          href: sb.data.url.getObjectPageParams(
                            obj[name],
                            options
                          ),
                        });
                      }
                    };
                  }
                  break;
              }
            }
          }

          // special fields
          switch (name) {
            case "name": // treat as title
              fieldsForSubview[name].view = FieldView.bind(
                { ViewState: ViewState, options: options },
                name,
                "title",
                field
              );
              fieldsForSubview[name].type = "title";
              break;
          }

          // standard field types
          if (field.type) {
            fieldsForSubview[name].type = field.type;

            switch (field.type) {
              case "address":
              case "attachment":
              case "checklist":
              case "dashboard":
              case "duration":
              case "title":
              case "comments":
              case "currency":
              case "detail":
              case "edge":
              case "image":
              case "user":
              case "users":
              case "date":
              case "date-rollup":
              case "type":
              case "state":
              case "next-due-date":
              case "plain-text":
              case "priority":
              case "phone":
              case "service":
              case "timer":
              case "toggle":
              case "locations":
              case "icon":
              case "formula":
              case "reference-calculation":
                fieldsForSubview[name].view = FieldView.bind(
                  {
                    ViewState: ViewState,
                    options: options,
                    blueprint: Blueprint,
                  },
                  name,
                  field.type,
                  field
                );
                break;

              case "type":
                fieldsForSubview[name].view = function (c, obj) {
                  if (obj[name]) {
                    c.makeNode("t", "div", {
                      text: obj[name].name,
                      css: "ui label",
                    });
                  }
                };

                break;
            }
          }

          if (typeof field.view === "function") {
            fieldsForSubview[name].view = field.view;
          }
        }
      });

      // tags field
      if (options.fields.hasOwnProperty("tags")) {
        if (options.fields.tags.view() !== false) {
          fieldsForSubview.tagged_with = {
            title: "Tags",
            name: "tagged_with",
            view: function (c, obj) {
              var filteredTags = _.filter(obj.tagged_with, function (tagId) {
                return (
                  _.findWhere(Pools, { id: tagId }) &&
                  _.findWhere(Pools, { id: tagId }).group_type !== "Team"
                );
              });

              if (_.isEmpty(filteredTags)) {
                return false;
              }

              _.each(filteredTags, function (tagId) {
                tag_ui(c, _.findWhere(Pools, { id: tagId }), {
                  size: "mini",
                });
              });
            },
          };
        }
      } else {
        fieldsForSubview.tagged_with = {
          title: "Tags",
          name: "tagged_with",
          view: function (c, obj) {
            var filteredTags = _.filter(obj.tagged_with, function (tagId) {
              return (
                _.findWhere(Pools, { id: tagId }) &&
                _.findWhere(Pools, { id: tagId }).group_type !== "Team"
              );
            });

            if (_.isEmpty(filteredTags)) {
              return false;
            }

            _.each(filteredTags, function (tagId) {
              tag_ui(c, _.findWhere(Pools, { id: tagId }), {
                size: "mini",
              });
            });
          },
        };
      }

      if (options) {
        if (options.isGrouped) {
          isGrouped = options.isGrouped;
          ViewState.isGrouping = true;
        }
      }

      ViewType.view(ui, prepare_data(data, options, ViewType), {
        actions: options.actions,
        actions_ui: options.actions_ui,
        blueprint: Blueprint,
        batch_actions_ui: batch_actions_ui.bind({}, options),
        create_ui: function (ui, templateObj) {
          return;
        },
        create_action: function (template) {
          var seed = getNewSeed(template);

          View["large-modal"].empty();
          View["large-modal"].patch();

          if (
            options.objectType == "contracts" &&
            options.state.pageObject.group_type == "Project"
          ) {
            if (options.state.pageObject.hasOwnProperty("main_contact")) {
              if (!options.state.pageObject.main_contact) {
                View["large-modal"].show();
              }
            }
          } else if (options.objectType != "contracts") {
            View["large-modal"].show();
          }
          //ui, newObj, onComplete, bp, opts
          options.actions.create(
            View["large-modal"],
            seed,
            function (response, keepOpen) {
              if (!keepOpen) {
                View["large-modal"].hide();
              }

              setTimeout(function () {
                if (Array.isArray(Data)) {
                  Data.unshift(response);
                } else {
                  Data = [response];
                }

                body_ui(View.Body, options, Data);
                View.Body.patch();
              }, 1);
            },
            Blueprint,
            {
              setTemplateProps: templateForm_ui,
              options: options,
            }
          );
        },
        create_fromTemplate_action: function (floater, template) {
          var seed = getNewSeed(template);

          View["large-modal"].empty();
          View["large-modal"].patch();

          options.actions.createFromTemplate(
            floater,
            seed,
            function (response, keepOpen) {
              setTimeout(function () {
                if (Array.isArray(Data)) {
                  Data.unshift(response);
                } else {
                  Data = [response];
                }

                body_ui(View.Body, options, Data);
                View.Body.patch();
              }, 1);
            },
            Blueprint,
            {
              setTemplateProps: templateForm_ui,
              options: options,
              View: View["large-modal"],
            }
          );

          floater.container.patch;
        },
        config: options.config,
        editBlueprint: options.editBlueprint,
        fields: fieldsForSubview,
        filterSelections: filterSelections,
        emptyMessage: (function () {
          if (typeof options.emptyMessage === "string") {
            return options.emptyMessage;
          }
          if (options.actions && options.actions.hasOwnProperty("create")) {
            var emptyMessage = "";
            return emptyMessage;
          } else {
            return "No saved items.";
          }
        })(),
        entity_type: EntityType,
        groupBy: options.groupBy,
        groupings: options.groupings,
        isGrouped: isGrouped,
        isLoading: isLoading || false,
        isSortable: options.isSortable,
        metrics: optionsMetrics,
        open: options.open,
        onMobile: OnMobile,
        query: Query,
        rangeOver: options.rangeOver,
        size: options.size,
        subviews: options.subviews,
        types: Types,

        // callbacks
        archiveSelectedItems: archive_selected_items,
        createGroup: function (
          data,
          customView,
          callback,
          options,
          btn,
          group
        ) {
          createGroup(data, customView, callback, options, btn, group);
        },
        getDataByRange: function (range, callback) {
          get_data(
            options,
            Query,
            function (object_list, data) {
              callback(data);
            },
            Page,
            Types,
            ViewType,
            true,
            range
          );
        },
        get_child_data: get_child_data,
        getCounts: function (groupBy, dateField, callback) {
          get_counts(options, Query, groupBy, dateField, callback);
        },
        getSum: function (x, y, callback, chartDataSetup) {
          if (chartDataSetup !== undefined) {
            options.chartDataSetup = chartDataSetup;
          }

          get_chart_data(options, Query, x, y, callback);
        },
        getRawEventData: function (callback) {
          get_raw_event_data(callback);
        },
        onDraw: onDraw || function () {},
        onGroupReorder: function (group, groupNext) {
          var currentWorkflow = {};

          if (EntityType) {
            currentWorkflow =
              EntityType.blueprint[options.groupBy.field].workflow;
          } else {
            currentWorkflow = _.findWhere(Types, { id: Query.type });
          }

          sb.notify({
            type: "update-state-flow",
            data: {
              current: group,
              next: groupNext,
              operation: "reorder",
              type: currentWorkflow,
              onComplete: function (updatedStates) {},
            },
          });
        },
        singleView: function (obj, ui, viewOptions) {
          if (_.isEmpty(viewOptions)) {
            viewOptions = {};
          }
          if (ui) {
            single_ui(ui, options, obj, viewOptions);
          } else {
            viewOptions.inModal = true;
            single_ui(View["large-modal"], options, obj, viewOptions);
          }

          options.open = obj.id;
        },
        refresh: function (updatedData, updatedBp) {
          if (updatedData) {
            Data.data = updatedData;
          }
          if (
            updatedBp &&
            typeof options.getFieldsFromBlueprint === "function"
          ) {
            Blueprint = updatedBp.blueprint;
            options.fields = options.getFieldsFromBlueprint(
              updatedBp.blueprint
            );
          }

          refresh_pool(View.Body, options);
        },
        setOptions: function (newOptions) {
          options = resetOptions(newOptions);
          refresh_pool(View.Body, options);
        },
        toggleSort: toggleSort,
        updateGroup: function (data, callback) {
          function updateUI(updatedObj) {
            var movedObj = _.findWhere(Data, { id: data.objectId });
            var beforeObj = _.findWhere(Data, { id: data.sort.before }) || {};
            var afterObj = _.findWhere(Data, { id: data.sort.after }) || {};
            var movedObjIndex = _.findIndex(Data, { id: movedObj.id });
            var beforeObjIndex =
              beforeObj.hasOwnProperty("id") && beforeObj.id
                ? _.findIndex(Data, { id: beforeObj.id })
                : 0;
            var afterObjIndex =
              afterObj.hasOwnProperty("id") && afterObj.id
                ? _.findIndex(Data, { id: afterObj.id })
                : 0;
            var targetObjIndex = _.findIndex(Data, { id: movedObj.id });
            var shouldUpdateUI =
              movedObj[options.groupBy.field] ===
              updatedObj[options.groupBy.field];

            if (!_.isEmpty(afterObj)) {
              targetObjIndex =
                movedObjIndex > afterObjIndex
                  ? afterObjIndex + 1
                  : afterObjIndex; // if move upward add 1, otherwise leave alone
              targetObjIndex = targetObjIndex == 0 ? 1 : targetObjIndex; // if moving to second position
              targetObjIndex =
                targetObjIndex > Data.length ? Data.length : targetObjIndex; // if moving to last position
            } else if (!_.isEmpty(beforeObj)) {
              targetObjIndex = beforeObjIndex - 1;
              targetObjIndex = targetObjIndex == -1 ? 0 : targetObjIndex; // if moving to first position
            }

            Data[_.indexOf(Data, movedObj)] = updatedObj;

            Data.splice(targetObjIndex, 0, Data.splice(movedObjIndex, 1)[0]);

            if (shouldUpdateUI) {
              // update ui
              body_ui(View.Body, options, Data);
            }
          }

          // if a custom entity, updateState
          if (options.objectType.charAt(0) === "#") {
            sb.data.db.controller(
              "updateSortAndState",
              data,
              function (updatedObj) {
                updateUI(updatedObj);

                if (typeof callback === "function") {
                  callback();
                }
              }
            );
          } else if (
            options.groupings &&
            options.groupings[options.groupBy.field] &&
            options.groupings[options.groupBy.field].options
          ) {
            sb.data.db.obj.update(
              options.objectType,
              {
                id: data.objectId,
                [options.groupBy.field]:
                  options.groupings[options.groupBy.field].options[group].value,
              },
              function (updatedObj) {
                refresh_pool(View.Body, options);
              }
            );
          } else if (options.groupBy.field) {
            if (
              options.fields &&
              options.fields[options.groupBy.field] &&
              options.fields[options.groupBy.field].type === "state"
            ) {
              sb.data.db.controller(
                "updateSortAndState",
                data,
                function (updatedObj) {
                  updateUI(updatedObj);

                  if (typeof callback === "function") {
                    callback();
                  }
                }
              );
            } else {
              sb.data.db.obj.update(
                options.objectType,
                {
                  id: data.objectId,
                  [options.groupBy.field]: group,
                },
                function (updatedObj) {
                  // update data
                  _.findWhere(Data, { id: data.objectId })[
                    options.groupBy.field
                  ] = updatedObj[options.groupBy.field];

                  // update ui
                  body_ui(View.Body, options, Data);
                },
                1
              );
            }
          }
        },
        updateOrder: function (item, before, after) {
          $("#loader").fadeIn();
          setTimeout(function () {
            $("#loader").fadeOut();
          }, 500);
        },
        updateSelection: function (selection) {
          CurrentSelection = selection;
        },
      });

      if (!options.hidePagingUnder) {
        if (options.showPaging === true || options.showPaging === undefined) {
          // only show paging btns if more than one page
          if (numPages > 1) {
            page_nav_ui(
              ui.makeNode("under", "div", { css: "ui tiny pagination menu" }),
              Query,
              Data
            );
          }
        }
      }

      if (options.counter) {
        if (data) {
          counter = data.length;

          ui.makeNode("lb_counter", "lineBreak", { spaces: 1 });

          ui.makeNode("counter", "div", {});

          ui.counter.makeNode("test", "div", {
            text: "Total # of items loaded: <strong>" + counter + "</strong>",
          });
        } else {
          counter = 0;
        }
      }
    }

    function loader_ui(ui) {
      ui.makeNode("loader", "div", {
        css: "ui tiny indicating progress",
        // 				css:'animated fadeIn ui blue tiny bottom attached progress',
        style: "height:2px !important;",
        listener: {
          type: "progress",
        },
      }).makeNode("bar", "div", {
        css: "bar",
        style: "height:2px !important;",
      });
    }

    function update_loader(ui, percent, shouldFade, dontAnimate) {
      // 'set percent' animates, 'update progress' does not
      var updateType = "set percent";
      if (dontAnimate) {
        updateType = "reset";
      }

      // update percentage
      setTimeout(function () {
        $(ui.loader.selector).progress(updateType, percent);
      }, 1);

      // fade out if complete
      if (shouldFade) {
        if (closeLoader) {
          clearTimeout(closeLoader);
        }

        closeLoader = setTimeout(function () {
          ui.loader.addClass("animated fadeOut");
          $(ui.loader.selector).progress("reset");

          /*
setTimeout(function(){

						$(ui.loader.selector).progress(updateType, 0);

					}, 200);
*/
        }, 3000);
      }
    }

    function single_ui(modal, options, item, viewOptions) {
      function openSingleView(modal, objectData) {
        function reopenModal(options, item) {
          sb.notify({
            type: "get-sys-modal",
            data: {
              callback: function (modal) {
                single_ui(modal, options, item, { inModal: true });
              },
            },
          });
        }

        var reopenModalCallback = false;
        if (!_.isEmpty(viewOptions) && viewOptions.inModal) {
          viewOptions.goBack = function (obj) {
            reopenModal(options, item);
          };
        }

        if (
          options.singleView &&
          typeof options.singleView.view === "function"
        ) {
          modal.body.singleArea.empty();
          options.singleView.view(
            modal.body.singleArea,
            objectData,
            function (ready) {
              if (ready) {
                if (ready.dom) {
                  ready.dom.patch();
                }
                if (ready.after) {
                  ready.after(ready.dom);
                }
              } else {
                ui.patch();
              }
            },
            function (refresh) {
              if (refresh) {
                refresh_pool(ui.Body, options);
              }

              modal.hide();
            },
            options,
            viewOptions
          );
        } else {
          _.each(fieldsForSubview, function (field, name) {
            if (field.view) {
              field.view(
                modal.body.singleArea.makeNode(name, "div", {
                  css: "description",
                  style: "padding:8px;",
                }),

                item
              );
            } else {
              modal.body.singleArea.empty();

              modal.body.singleArea.makeNode(name, "div", {
                css: "description",
                text: item[name],
                style: "padding:8px;",
              });
            }
          });

          modal.body.makeNode("comments", "div", {});
          modal.body.patch();

          sb.notify({
            type: "show-note-list-box",
            data: {
              domObj: modal.body.comments,
              objectId: item.id,
              collapse: true,
            },
          });
        }
      }

      modal.body.empty();

      modal.body.makeNode("menu", "div", {
        css: "ui secondary right floated menu",
        style: "margin:0 !important;",
      });

      var linkSetup = {
        css: "circular icon button item",
        text: '<i class="external square alternate icon"></i>',
        tag: "a",
        href: sb.data.url.getObjectPageParams(item, options),
      };

      var closeLinkSetup = {
        css: "circular icon button item",
        text: '<i class="close icon"></i>',
        tag: "a",
      };

      if (options.singleView && options.singleView.action) {
        delete linkSetup.href;
      }

      if (options.singleView && typeof options.singleView.link === "function") {
        linkSetup.href = options.singleView.link(item);
      }

      modal.body.menu.makeNode("open", "div", linkSetup);

      if (!_.isEmpty(viewOptions) && typeof viewOptions.close === "function") {
        modal.body.menu.makeNode("close", "div", closeLinkSetup).notify(
          "click",
          {
            type: "collections-run",
            data: {
              run: function (item) {
                viewOptions.close(item);
              }.bind({}, item),
            },
          },
          sb.moduleId
        );
      } else if (typeof modal.hide === "function") {
        modal.body.menu.makeNode("close", "div", closeLinkSetup).notify(
          "click",
          {
            type: "collections-run",
            data: {
              run: function () {
                modal.hide();
              },
            },
          },
          sb.moduleId
        );
      }

      if (options.singleView && options.singleView.action) {
        modal.body.menu.open.notify(
          "click",
          {
            type: "collections-run",
            data: {
              run: function (item) {
                options.singleView.action(item);
              }.bind({}, item),
            },
          },
          sb.moduleId
        );
      }

      if (options.singleView && options.singleView.action === "hide") {
        delete modal.body.menu.open;
      }

      // 			modal.body.menu.makeNode('left', 'div', {text: '<i class="left chevron icon"></i>', css: 'icon link item'});
      // 			modal.body.menu.makeNode('right', 'div', {text: '<i class="right chevron icon"></i>', css: 'icon link item'});

      modal.body
        .makeNode("singleArea", "div", { css: "" })
        .makeNode("loading", "div", { css: "ui active inverted dimmer" })
        .makeNode("loader", "div", { css: "ui loader" });

      modal.show();
      modal.body.patch();

      if (options && options.singleView && options.singleView.useCache) {
        openSingleView(modal, item);
      } else {
        var selection = 1;
        if (options.singleView && options.singleView.select) {
          selection = options.singleView.select;
        }

        sb.data.db.obj.getById(
          options.objectType,
          item.id,
          function (objectData) {
            modal.body.singleArea.empty();
            openSingleView(modal, objectData);
          },
          selection,
          true
        );
      }
    }

    function comments_ui(modal, item, options) {
      modal.body.empty();

      modal.body.makeNode("header", "div", {
        tag: "h2",
        text: "Comments",
        css: "ui header",
      });
      modal.body.makeNode("divider", "div", {
        css: "ui divider",
      });
      modal.body.makeNode("body", "div", {});

      modal.body.patch();

      sb.notify({
        type: "show-note-list-box",
        data: {
          domObj: modal.body.body,
          objectIds: [item.id],
          objectId: item.id,
          collapse: "open",
          activityFeed: true,
        },
      });

      modal.show();
    }

    function tag_ui(ui, tag, tagOptions) {
      function get_tag_display_text(tag) {
        var tagColor = tag.color || "";
        var tagIcon = tag.icon || "hashtag";

        switch (tag.object_bp_type) {
          case "users":
            return '<i class="user icon"></i> ' + tag.fname + " " + tag.lname;

          case "groups":
            if (tag.group_type === "Project") {
              return '<i class="project diagram icon"></i> ' + tag.name;
            } else {
              return '<i class="users icon"></i> ' + tag.name;
            }

          case "system_tags":
            return `<i class="${tagColor} ${tagIcon} icon"></i> ${tag.tag}`;
        }
      }

      if (tag === undefined) {
        return;
      }

      var styleTest = "margin:0px;border-radius:0px;";
      var tagSize = "";
      if (tagOptions && tagOptions.size) {
        tagSize = tagOptions.size;
      }

      // set default color to blue
      if (!tag.color) {
        tag.color = "#027eff";
      }

      var style = "background-color:white;color:" + tag.color + ";";

      if (Query.tagged_with && _.contains(Query.tagged_with, tag.id)) {
        style = "background-color:" + tag.color + ";color:white;";
      }

      if (tag.layer && tag.layer === "hq") {
        style = "background-color:" + tag.color + ";color:white;";
      }

      return ui.makeNode("pool-" + tag.id, "div", {
        text: get_tag_display_text(tag),
        css: "ui " + tagSize + " label show-pointer",
        style: style,
      });
    }

    function refresh_pool(ui, options, callback) {
      // !FEATURE BLOCK -- SAVING COLLECTIONS PAGES (01-27-20)
      if (false) {
        if (options.config !== undefined) {
          if (options.config.hasOwnProperty("page")) {
            //Page.page = parseInt(options.config.page);
          }
        }
      }

      update_loader(View.loader, 22, true, true);
      View.loader.loader.removeClass("animated fadeOut");

      get_data(
        options,
        Query,
        function (object_list, data) {
          Data = object_list;
          update_loader(View.loader, 80, true);

          get_types(
            options,
            function (types) {
              Types = types;

              if (_.isEmpty(options.groupBy)) {
                setGroupBy(options);
              }

              // update loader state
              update_loader(View.loader, 100, true);

              Page.pageLength = options.pageLength;
              Page.recordsFiltered = data.recordsFiltered;
              Page.recordsTotal = data.recordsTotal;

              ui.empty();

              body_ui(ui, options, Data);

              ui.patch();

              CurrentSelection = [];

              $(".ui.sticky").sticky("refresh");

              $("#loader").fadeOut();

              if (callback !== undefined) {
                callback(object_list);
              }

              if (
                (appConfig.instance === "rickyvoltz" ||
                  appConfig.instance === "voltzsoftware") &&
                appConfig.debug &&
                appConfig.debug.collectionsRedraw
              ) {
                var collMetaData = _.findWhere(Collections, {
                  _id: options._id,
                });

                if (collMetaData.interval) {
                  clearInterval(collMetaData.interval);
                }

                collMetaData.interval = setInterval(function () {
                  get_data(
                    options,
                    Query,
                    function (object_list, data) {
                      Data = object_list;
                      Page.pageLength = options.pageLength;
                      Page.recordsFiltered = data.recordsFiltered;
                      Page.recordsTotal = data.recordsTotal;

                      ui.empty();

                      body_ui(ui, options, Data);

                      var start = new Date();
                      ui.patch();

                      if (typeof updateLastRefreshUi === "function") {
                        updateLastRefreshUi();
                      }

                      var end = new Date();
                    },
                    Page,
                    Types,
                    ViewType,
                    undefined,
                    undefined,
                    true
                  );
                }, 5000);
              }
            },
            Blueprint
          );
        },
        Page,
        Types,
        ViewType,
        null,
        false,
        options.bypassCache
      );
    }

    options = resetOptions(options);

    // update subview specific options
    if (
      options.subviews &&
      options.subviews[State.type] &&
      options.subviews[State.type].options
    ) {
      options = resetOptions(options.subviews[State.type].options);
    }

    ui.empty();
    ui.makeNode("loader", "div", { style: "display:none;" });
    if (options.modalSize !== false) {
      ui.makeNode("large-modal", "modal", {
        size: options.modalSize,
        fullScreen: false,
        onClose: onLargeModalClose,
      });
    } else {
      ui.makeNode("large-modal", "modal", {
        size: "large",
        onClose: onLargeModalClose,
      });
    }

    ui.patch();

    loader_ui(ui.loader);

    ui.makeNode("pools", "div", {
      css: "pools",
      style:
        "background-color: white !important; margin-bottom:5px; width:100%;",
    });
    ui.makeNode("h", "div", {
      style: "width:100%;",
    });
    ui.makeNode("Body", "div", {
      css: "ui attached basic segment body",
      style:
        "padding-left:0; padding-right:0; padding-top:0px; padding-bottom:0px; width:100%;",
    });

    cache_blueprint(
      options.objectType,
      function (r) {
        // update loader state
        update_loader(ui.loader, 13, false);

        get_types(
          options,
          function (types) {
            // update view cache
            Types = types;

            // initialize view flags
            if (OnMobile && options.selectedMobileView) {
              ViewType = _.findWhere(views, {
                name: options.selectedMobileView,
              });
            } else if (options.selectedView) {
              ViewType = _.findWhere(views, {
                name: options.selectedView,
              });
            } else {
              ViewType = _.findWhere(views, {
                default: true,
              });
            }

            // The 'hideTimeRangeFilter' seems to pre-exist on the ViewType object as true. This is not the desired
            // functionality. It will be removed here then added only if it exists on the options object.

            if (ViewType) {
              if (ViewType.hasOwnProperty("hideTimeRangeFilter")) {
                delete ViewType.hideTimeRangeFilter;
              }
            }

            // Checking subview flags to add to viewType object
            if (
              options.hasOwnProperty("subviews") &&
              options.subviews !== undefined
            ) {
              _.each(options.subviews[ViewType.name], function (v, k) {
                ViewType[k] = v;
              });
            }

            //!WORKING HERE:
            // 	need to fix default time range in tasks report
            if (
              options.subviews &&
              options.subviews[ViewType.name] &&
              options.subviews[ViewType.name].range &&
              options.subviews[ViewType.name].range.defaultTo
            ) {
              Query.range = options.subviews[ViewType.name].range.defaultTo;
            }

            if (_.isEmpty(options.groupBy)) {
              setGroupBy(options);
            }

            body_ui(ui.Body, options, Data, true);

            if (!options.shouldKeepSegment) {
              //$(ui.selector).parent().removeClass('ui basic segment');
              // 					$(ui.selector).css('margin', '-1em');
            }

            // update loader state
            update_loader(ui.loader, 26, false);

            // update loader state
            update_loader(ui.loader, 42, false);

            header_ui(ui.h, options, []);

            $(ui.pools.selector).addClass("ui attached segment");

            if (
              options.tags === undefined ||
              options.tags === true ||
              (Array.isArray(options.tags) && options.tags.length > 0)
            ) {
              pool_ui(ui.pools, Query, []);
            }

            if (options.modalSize !== false) {
              ui.makeNode("large-modal", "modal", {
                size: options.modalSize,
                fullScreen: false,
                onClose: onLargeModalClose,
              });
            } else {
              ui.makeNode("large-modal", "modal", {
                size: "large",
                onClose: onLargeModalClose,
              });
            }

            ui.patch();

            refresh_pool(ui.Body, options, function (data) {
              Data = data;

              header_ui(ui.h, options, []);

              ui.patch();
            });
          },
          Blueprint
        );
      },
      options
    );
  }

  // indexedDB/caching manipulation functions

  function set_collectionsId(collectionsSetup, callback) {
    var layer = detect_layer(appConfig.state);
    var objectType = collectionsSetup.objectType;

    if (
      objectType === "groups" &&
      collectionsSetup.where.hasOwnProperty("group_type")
    ) {
      objectType += "_" + collectionsSetup.where.group_type;
    }

    if (
      collectionsSetup.hasOwnProperty("state") &&
      collectionsSetup.state !== undefined
    ) {
      if (collectionsSetup.state.hasOwnProperty("_toolId")) {
        objectType += "_" + collectionsSetup.state._toolId.toString();
      } else if (
        collectionsSetup.state.hasOwnProperty("tool") &&
        collectionsSetup.state.tool &&
        collectionsSetup.state.tool.id
      ) {
        objectType += "_" + collectionsSetup.state.tool.id.toString();
      }
    }

    var collectionsObjectTypeUID = "collections_" + layer + "_" + objectType;

    if (collectionsSetup.config === undefined) {
      collectionsSetup.config = {};
    }

    collectionsSetup.config.colUID = collectionsObjectTypeUID;

    if (callback) {
      callback(collectionsObjectTypeUID);
    }
  }

  function get_cookie() {
    return document.cookie;
  }

  function read_cookie(cookie, coll_id) {
    var cookieArr = cookie.split(";");
    var obj = {};
    var collectionsCookieObjAmount = 0;
    var originalColId = coll_id;

    _.each(cookieArr, function (val) {
      var valSplit = val.split("=");

      if (valSplit[0].search("collections_") !== -1) {
        collectionsCookieObjAmount++;
      }

      valSplit[0] = valSplit[0].replace(/_/g, "").replace(/\s/g, "");

      coll_id = coll_id.replace(/_/g, "");

      if (valSplit[0] == coll_id) {
        obj[valSplit[0]] = valSplit[1];
      }
    });

    if (collectionsCookieObjAmount > 0) {
      _.each(cookieArr, function (val) {
        var valSplit = val.split("=");

        if (
          valSplit[0].search("collections") !== -1 &&
          valSplit[0] !== originalColId
        ) {
          document.cookie =
            valSplit[0] + "=; expires=Thu, 01 Jan 1970 00:00:00 GMT;";
        }
      });
    }

    if (!_.isEmpty(obj)) {
      return obj;
    } else {
      return false;
    }
  }

  function detect_layer(state) {
    if (
      state.hasOwnProperty("myStuff") ||
      state.hasOwnProperty("profileSetup") ||
      (state.hasOwnProperty("layer") && state.layer === "myStuff")
    ) {
      return "myStuff";
    } else if (state.hasOwnProperty("team")) {
      return "team";
    } else if (state.hasOwnProperty("project")) {
      return "project";
    } else if (state.hasOwnProperty("object")) {
      return "object";
    } else {
      return "hq";
    }
  }

  function setup_cache(collectionsSetup, callback) {
    set_collectionsId(collectionsSetup, function (collectionsObjectTypeUID) {
      // Read database
      sb.notify(
        {
          type: "get-from-browser-cache",
          data: {
            id: collectionsObjectTypeUID,
            onComplete: function (res) {
              // if collections exist
              if (res) {
                collectionsSetup.config = _.extend(
                  collectionsSetup.config,
                  res
                );

                callback(collectionsSetup);

                // Read cookie data and remove it
                if (
                  read_cookie(get_cookie(), collectionsObjectTypeUID) === false
                ) {
                  read_cookie(get_cookie(), collectionsObjectTypeUID);
                } else {
                  read_cookie(get_cookie(), collectionsObjectTypeUID);
                }
              } else {
                // if it does not, create it

                sb.notify(
                  {
                    type: "save-to-browser-cache",
                    data: {
                      obj: {
                        id: collectionsObjectTypeUID,
                        subview: "default",
                        page: 1,
                        pageLength: 0,
                        fields: [],
                        subviews: {
                          table: {},
                          board: {},
                          list: {},
                          cards: {},
                        },
                      },
                      onComplete: function (newRes) {
                        collectionsSetup.config = _.extend(
                          collectionsSetup.config,
                          newRes
                        );

                        callback(collectionsSetup);

                        // Read cookie data and remove it
                        if (
                          read_cookie(
                            get_cookie(),
                            collectionsObjectTypeUID
                          ) === false
                        ) {
                          read_cookie(get_cookie(), collectionsObjectTypeUID);
                        } else {
                          read_cookie(get_cookie(), collectionsObjectTypeUID);
                        }
                      },
                    },
                  },
                  sb.moduleId
                );
              }
            },
            override: function () {
              collectionsSetup.config = {};

              callback(collectionsSetup);
            },
          },
        },
        sb.moduleId
      );
    });
  }

  function update_browserCache(options, cb) {
    sb.notify(
      {
        type: "update-browser-cache",
        data: {
          id: options.config.colUID,
          callback: function (data, callback) {
            cb(data, function (modified) {
              callback(modified);
            });
          },
        },
      },
      sb.moduleId
    );
  }

  return {
    // LIFE CYCLE

    initListeners: function () {
      sb.listen({
        "collections-run": this.run,
        "register-collection-view": this.registerView,
        "show-collection": this.show,
        "page-changed": this.clear,
      });
    },

    init: function () {},

    load: function () {},

    destroy: function () {},

    // RUN

    run: function (data) {
      data.run(data);
    },

    show: function (setup) {
      var pools = [];
      var selectedPools = [];

      if (
        setup.state &&
        setup.state.team &&
        _.isEmpty(setup.state.project) &&
        !setup.state.ignoreTeamTagFromCollectionsFile
      ) {
        pools = _.without(setup.state.team.tagged_with, 0);
        pools.unshift(setup.state.team.id);

        switch (typeof setup.state.team.read[0]) {
          case "number":
            pools = setup.state.team.read.slice(0);
            break;

          case "object":
            pools = pools.concat(_.pluck(setup.state.team.read, "id"));
            break;
        }

        selectedPools = [setup.state.team.id];
      }

      if (setup.selectedTags) {
        selectedPools = selectedPools.concat(setup.selectedTags);
      }

      if (setup.hiddenTags) {
        selectedPools = selectedPools.concat(setup.hiddenTags);
      }

      if (Array.isArray(setup.tags)) {
        pools = setup.tags;
      }

      var selectedView = setup.selectedView || "table";
      var pageLength = setup.pageLength || 25;

      var collectionsSetup = {
        actions: setup.actions,
        allTagsAreToggleable: setup.allTagsAreToggleable,
        canSearchForTags: setup.canSearchForTags,
        editBlueprint: setup.editBlueprint || false,
        entityType: setup.entityType, // optional, used by entity collections
        entityTypes: setup.entityTypes, // optional, used by entity collections
        subTypes: setup.subTypes || false, // optional, used by entity collections
        header: setup.header,
        fields: setup.fields,
        filters: setup.filters,
        advancedFilters: setup.advancedFilters,
        fullView: setup.fullView,
        groupings: setup.groupings,
        hiddenTags: setup.hiddenTags,
        hideDateRange: setup.hideDateRange,
        ignorePageFilter: setup.ignorePageFilter,
        isSortable: setup.isSortable,
        layer: setup.layer,
        metrics: setup.metrics,
        minTagSelection: setup.minTagSelection,
        maxTagSelection: setup.maxTagSelection,
        modalSize: setup.modalSize || false,
        objectType: setup.objectType,
        pool: setup.pool,
        query: setup.query,
        size: setup.size,
        singleView: setup.singleView,
        where: setup.where,
        selectedMobileView: setup.selectedMobileView,
        selectedView: selectedView,
        pageLength: pageLength,
        parseData: setup.parseData,
        rangeOver: setup.rangeOver,
        shouldKeepSegment: setup.shouldKeepSegment || false,
        selectedTags: setup.selectedTags,
        sortCol: setup.sortCol,
        sortDir: setup.sortDir,
        sortCast: setup.sortCast,
        sortCsv: setup.sortCsv || false, // allows to make the csv export sortable
        subviews: setup.subviews,
        typeTemplate: setup.typeTemplate,
        menu: setup.menu, // turns off the collections menu if set to false
        submenu: setup.submenu, // turns off the collections submenu if set to false
        search: setup.search, // turns off the search box if set to false
        searchAboveCollection: setup.searchAboveCollection,
        emptyMessage: setup.emptyMessage,
        hidePagingAbove: setup.hidePagingAbove,
        hidePagingUnder: setup.hidePagingUnder,
        showPaging: setup.showPaging,
        templates: setup.templates,
        config: setup.config,
        filterBy: setup.filterBy, //
        tags: setup.tags,
        onBoxview: setup.onBoxview,
        counter: setup.counter,
        showDateRange: setup.showDateRange,
        bypassCache: setup.bypassCache,

        // entity mgmt callbacks
        getFieldsFromBlueprint: setup.getFieldsFromBlueprint,
        editSubType: setup.editSubType,

        // from state
        state: setup.state,
        parent: setup.parent,
        pools: pools,
        selectedPools: selectedPools,
        tool: setup.tool,
        entity: setup.entity,

        // callbacks
        data: setup.data, // callback that return collections data

        // coll id
        _id:
          Math.random().toString(36).substring(2, 15) +
          Math.random().toString(36).substring(2, 15),
      };

      if (setup.tagDeselection) {
        collectionsSetup.tagDeselection = true;
      }

      if (setup.isGrouped) {
        collectionsSetup.isGrouped = setup.isGrouped;
      }

      if (setup.hasOwnProperty("data")) {
        if (setup.data.hasOwnProperty("add")) {
          collectionsSetup.data.add = setup.data.add;
        }

        if (setup.data.hasOwnProperty("remove")) {
          collectionsSetup.data.remove = setup.data.remove;
        }

        if (setup.data.hasOwnProperty("update")) {
          collectionsSetup.data.update = setup.data.update;
        }

        if (setup.data.hasOwnProperty("obj")) {
          collectionsSetup.data.obj = setup.data.obj;
        }
      }

      if (
        collectionsSetup.onBoxview === undefined ||
        collectionsSetup.onBoxview === false
      ) {
        setup_cache(collectionsSetup, function (modifiedCollectionsSetup) {
          collectionsSetup = modifiedCollectionsSetup;

          collection_ui(
            setup.domObj,

            collectionsSetup,

            setup.draw
          );
        });
      } else {
        collection_ui(
          setup.domObj,

          collectionsSetup,

          setup.draw
        );
      }
    },

    clear: function () {
      _.each(Collections, function (coll) {
        if (coll.interval) {
          clearInterval(coll.interval);
        }
      });

      Collections = [];
    },

    // REGISTRATIONS

    registerView: function (registration) {
      registerView(registration);
    },
  };
});
