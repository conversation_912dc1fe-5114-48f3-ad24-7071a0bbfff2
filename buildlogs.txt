[Container] 2025/07/30 21:33:27.731934 Running on CodeBuild On-demand
[Container] 2025/07/30 21:33:27.732022 Waiting for agent ping
[Container] 2025/07/30 21:33:29.037194 Waiting for DOWNLOAD_SOURCE
[Container] 2025/07/30 21:33:31.407308 Phase is DOWNLOAD_SOURCE
[Container] 2025/07/30 21:33:31.412628 CODEBUILD_SRC_DIR=/codebuild/output/src813338945/src
[Container] 2025/07/30 21:33:31.414695 YAML location is /codebuild/output/src813338945/src/buildspec.yml
[Container] 2025/07/30 21:33:31.415250 Found possible syntax errors in buildspec:
In the section env
    The following keys cannot be identified:
        privilegedMode
[Container] 2025/07/30 21:33:31.424688 Setting HTTP client timeout to higher timeout for S3 source
[Container] 2025/07/30 21:33:31.424800 Processing environment variables
[Container] 2025/07/30 21:33:31.493374 No runtime version selected in buildspec.
[Container] 2025/07/30 21:33:31.532618 Moving to directory /codebuild/output/src813338945/src
[Container] 2025/07/30 21:33:31.532639 Cache is not defined in the buildspec
[Container] 2025/07/30 21:33:31.569867 Skip cache due to: no paths specified to be cached
[Container] 2025/07/30 21:33:31.570107 Registering with agent
[Container] 2025/07/30 21:33:31.599775 Phases found in YAML: 4
[Container] 2025/07/30 21:33:31.599794  PRE_BUILD: 10 commands
[Container] 2025/07/30 21:33:31.599798  BUILD: 37 commands
[Container] 2025/07/30 21:33:31.599800  POST_BUILD: 8 commands
[Container] 2025/07/30 21:33:31.599803  INSTALL: 11 commands
[Container] 2025/07/30 21:33:31.600110 Phase complete: DOWNLOAD_SOURCE State: SUCCEEDED
[Container] 2025/07/30 21:33:31.600119 Phase context status code:  Message:
[Container] 2025/07/30 21:33:31.703562 Entering phase INSTALL
[Container] 2025/07/30 21:33:31.737440 Running command aws --version
aws-cli/2.17.60 Python/3.12.6 Linux/4.14.355-275.572.amzn2.x86_64 exec-env/AWS_ECS_EC2 exe/x86_64.ubuntu.20

[Container] 2025/07/30 21:33:32.718972 Running command phpenv global 7.4
7.4

[Container] 2025/07/30 21:33:32.877743 Running command curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3

[Container] 2025/07/30 21:33:33.441104 Running command chmod 700 get_helm.sh

[Container] 2025/07/30 21:33:33.446462 Running command echo "Installing Helm 3.14.x"
Installing Helm 3.14.x

[Container] 2025/07/30 21:33:33.449920 Running command ./get_helm.sh --version v3.14.0
Downloading https://get.helm.sh/helm-v3.14.0-linux-amd64.tar.gz
Verifying checksum... Done.
Preparing to install helm into /usr/local/bin
helm installed into /usr/local/bin/helm

[Container] 2025/07/30 21:33:35.174765 Running command curl -o kubectl https://amazon-eks.s3.$AWS_DEFAULT_REGION.amazonaws.com/1.19.6/2021-01-05/bin/linux/amd64/kubectl
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   469    0   469    0     0  27588      0 --:--:-- --:--:-- --:--:-- 26055

[Container] 2025/07/30 21:33:35.201728 Running command chmod +x ./kubectl

[Container] 2025/07/30 21:33:35.205764 Running command mkdir -p $HOME/bin && cp ./kubectl $HOME/bin/kubectl && export PATH=$PATH:$HOME/bin

[Container] 2025/07/30 21:33:35.210711 Running command echo 'export PATH=$PATH:$HOME/bin' >> ~/.bashrc

[Container] 2025/07/30 21:33:35.215521 Running command . ~/.bashrc
/codebuild/output/tmp/script.sh: 13: /root/.bashrc: shopt: not found
/codebuild/output/tmp/script.sh: 21: /root/.bashrc: shopt: not found

[Container] 2025/07/30 21:33:35.258218 Phase complete: INSTALL State: SUCCEEDED
[Container] 2025/07/30 21:33:35.258238 Phase context status code:  Message:
[Container] 2025/07/30 21:33:35.292475 Entering phase PRE_BUILD
[Container] 2025/07/30 21:33:35.293457 Running command echo Logging in to Amazon ECR...
Logging in to Amazon ECR...

[Container] 2025/07/30 21:33:35.297447 Running command REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/bento

[Container] 2025/07/30 21:33:35.301046 Running command REPOSITORY_URI_MERGE=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/bento-merge-service

[Container] 2025/07/30 21:33:35.304633 Running command aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
Configure a credential helper to remove this warning. See
https://docs.docker.com/engine/reference/commandline/login/#credentials-store

Login Succeeded

[Container] 2025/07/30 21:33:36.004492 Running command aws eks --region $AWS_DEFAULT_REGION update-kubeconfig --name $AWS_CLUSTER_NAME
Added new context arn:aws:eks:us-east-2:************:cluster/bento to /root/.kube/config

[Container] 2025/07/30 21:33:36.768092 Running command echo "Checking cluster access..."
Checking cluster access...

[Container] 2025/07/30 21:33:36.774921 Running command chmod 600 /root/.kube/config

[Container] 2025/07/30 21:33:36.781726 Running command cat ~/.kube/config
apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJeU1UQXhNVEl3TlRreE0xb1hEVE15TVRBd09ESXdOVGt4TTFvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTE1xCjlXU0NCT09tTmhPZUxhYkNjUWRWY1BDRE1QbmpTWHBsUmp3d0RBc2N2ZG5YYUJIZXlKalAzdGtEbGZUdVNNaTgKeVRkRENldjRjc3RhWEVQcGZsbEdaZWd2bU5jbnphSVMyRkIyVFF5djRTMFFTZk1ZZjdJMS9UQ1AvVjR0WHpSTQphaERYcFJGVDNuRUFqbjJBeGVZQmlNZzNWek1HdXMycWZzK1N6dE9HWlNYOWdJeTJiOE9JR2liOFdPQW9jSHVsCnNWZXpSRDJISm5VdGdWKzFab1R1ZFNQS3IxVlVwQk9PN1hFd0NlZ0hvZ0RHSnNkUENTOXk2em9jY2p5MmVYNDIKRzNYSHQ2djl2UjFWL3oxL2VkNDhsWlVUU1lqTVlnRUJqbWhadys2bkhGS1htNjNXcFFyUWVveTI1M2VsUlc1SQpTTXVLMjJJTS9tdktTUTNRUVhFQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZPejE3UUpYTEdwcEl4TUxnS00vSWZpcWs0cGJNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSDQvK1g0bTR1YnB3RXV5MDloUQo0T3B5UkVITEJBVGczZ3crVDhJZTNBOFVsM1c2dDRlOTZOTDB4RWF2SVYwK3ZFWUsyYVdhenpSQ1l2cEoza0tKCmczT3ovbVUyekhwRE52V2RRVVRIb3NXNjFSL0I0NnhOWERuc1YvYWd0T3J0UVI0RDhPMk9HaTNhNDYyTjMyckMKVVhJOUtaVmR0bmNMbVRaNDFpSHdjK1lLSUF6cUhLK3BEMU4rdjkrR002TGRMTGxCMmxJTDNCYmJmaDJ4elc4OApIeVVmazVYYmNtdXBXS3Nianp0dUk3TFUvTmVxNjd5dEdMOTIyNE5uNU93clpCL2t3dTRhUnpXZStBN3psV0NaClNHSHRicVZjdFplUDY3TjR6azU4OTB6NnhKVkVaang5UVJuZ05nK0VUVHRpL0IzZ2xSU1hIQTZQWjhBQ0U4eGwKVUtzPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
    server: https://2F9726787F60E8D19EC268A3404A853F.gr7.us-east-2.eks.amazonaws.com
  name: arn:aws:eks:us-east-2:************:cluster/bento
contexts:
- context:
    cluster: arn:aws:eks:us-east-2:************:cluster/bento
    user: arn:aws:eks:us-east-2:************:cluster/bento
  name: arn:aws:eks:us-east-2:************:cluster/bento
current-context: arn:aws:eks:us-east-2:************:cluster/bento
kind: Config
preferences: {}
users:
- name: arn:aws:eks:us-east-2:************:cluster/bento
  user:
    exec:
      apiVersion: client.authentication.k8s.io/v1beta1
      args:
      - --region
      - us-east-2
      - eks
      - get-token
      - --cluster-name
      - bento
      - --output
      - json
      command: aws

[Container] 2025/07/30 21:33:36.788194 Running command kubectl get svc
NAME                  TYPE        CLUSTER-IP      EXTERNAL-IP   PORT(S)    AGE
bento-merge-service   ClusterIP   ************    <none>        8084/TCP   177d
bentoproduction       ClusterIP   *************   <none>        80/TCP     177d
kubernetes            ClusterIP   **********      <none>        443/TCP    2y293d

[Container] 2025/07/30 21:33:37.908606 Running command helm version
version.BuildInfo{Version:"v3.14.0", GitCommit:"3fc9f4b2638e76f26739cd77c7017139be81d0ea", GitTreeState:"clean", GoVersion:"go1.21.5"}

[Container] 2025/07/30 21:33:37.950911 Phase complete: PRE_BUILD State: SUCCEEDED
[Container] 2025/07/30 21:33:37.950927 Phase context status code:  Message:
[Container] 2025/07/30 21:33:37.986411 Entering phase BUILD
[Container] 2025/07/30 21:33:37.987301 Running command echo "Checking Docker version"
Checking Docker version

[Container] 2025/07/30 21:33:37.992868 Running command docker info
Client:
 Context:    default
 Debug Mode: false

Server:
 Containers: 0
  Running: 0
  Paused: 0
  Stopped: 0
 Images: 0
 Server Version: 20.10.24
 Storage Driver: overlay2
  Backing Filesystem: xfs
  Supports d_type: true
  Native Overlay Diff: true
  userxattr: false
 Logging Driver: json-file
 Cgroup Driver: cgroupfs
 Cgroup Version: 1
 Plugins:
  Volume: local
  Network: bridge host ipvlan macvlan null overlay
  Log: awslogs fluentd gcplogs gelf journald json-file local logentries splunk syslog
 Swarm: inactive
 Runtimes: io.containerd.runc.v2 io.containerd.runtime.v1.linux runc
 Default Runtime: runc
 Init Binary: docker-init
 containerd version: 2806fc1057397dbaeefbea0e4e17bddfbd388f38
 runc version:
 init version: de40ad0
 Security Options:
  seccomp
   Profile: default
 Kernel Version: 4.14.355-275.572.amzn2.x86_64
 Operating System: Ubuntu 20.04.6 LTS (containerized)
 OSType: linux
 Architecture: x86_64
 CPUs: 2
 Total Memory: 3.746GiB
 Name: ip-10-0-130-203.us-east-2.compute.internal
 ID: ITXI:MTUU:VALE:KWRW:QYJH:KGXE:3FK5:4SSQ:ZJRV:5VZO:2KHF:LXE2
 Docker Root Dir: /var/lib/docker
 Debug Mode: false
 Registry: https://index.docker.io/v1/
 Labels:
 Experimental: false
 Insecure Registries:
  *********/8
 Live Restore Enabled: false
 Product License: Community Engine

WARNING: API is accessible on http://127.0.0.1:2375 without encryption.
         Access to the remote API is equivalent to root access on the host. Refer
         to the 'Docker daemon attack surface' section in the documentation for
         more information: https://docs.docker.com/go/attack-surface/
WARNING: bridge-nf-call-iptables is disabled
WARNING: bridge-nf-call-ip6tables is disabled

[Container] 2025/07/30 21:33:38.015970 Running command echo Build started on `date`
Build started on Wed Jul 30 21:33:38 UTC 2025

[Container] 2025/07/30 21:33:38.027128 Running command cd ${CODEBUILD_SRC_DIR}

[Container] 2025/07/30 21:33:38.032724 Running command npm ci --cache .npm

> fsevents@1.2.13 install /codebuild/output/src813338945/src/node_modules/fsevents
> node install.js


Skipping 'fsevents' build as platform linux is not supported

> fomantic-ui@2.8.8 install /codebuild/output/src813338945/src/node_modules/fomantic-ui
> gulp install

Browserslist: caniuse-lite is outdated. Please run:
  npx browserslist@latest --update-db
  Why you should do it regularly: https://github.com/browserslist/browserslist#browsers-data-updating
[21:33:47] Using gulpfile /codebuild/output/src813338945/src/node_modules/fomantic-ui/gulpfile.js
[21:33:47] Starting 'install'...
·[2J·[0;0fCannot locate files to update at path:  /codebuild/output/src813338945/src/semantic/src/definitions
Running installer
[21:33:47] Starting 'run setup'...
[21:33:47] Finished 'run setup' after 498 μs
[21:33:47] Starting 'create install files'...
·[2J·[0;0fAuto-Installing (Without User Interaction)
------------------------------
Installing to semantic/
Copying UI definitions
Copying UI themes
Copying gulp tasks
Adding theme files
Creating gulpfile.js
Creating site theme folder /codebuild/output/src813338945/src/semantic/src/site/
[21:33:47] Starting 'create theme.config'...
Adjusting @siteFolder to:  site/
Creating src/theme.config (LESS config) /codebuild/output/src813338945/src/semantic/src/theme.config
[21:33:47] Finished 'create theme.config' after 19 ms
[21:33:47] Starting 'create semantic.json'...
Extending config file (semantic.json) /codebuild/output/src813338945/src/semantic.json
[21:33:47] Finished 'create semantic.json' after 13 ms
[21:33:47] Finished 'create install files' after 91 ms
[21:33:47] Starting 'clean up install'...


[21:33:47] Starting 'build'...
Building Semantic
[21:33:47] Starting 'build-css'...
[21:33:47] Starting 'Building uncompressed CSS'...
[21:33:47] Starting 'Building compressed CSS'...
[21:33:47] Created: ../../semantic/dist/components/reset.min.css
[21:33:47] Created: ../../semantic/dist/components/reset.css
[21:33:48] Created: ../../semantic/dist/components/site.min.css
[21:33:48] Created: ../../semantic/dist/components/site.css
[21:33:49] Created: ../../semantic/dist/components/button.min.css
[21:33:49] Created: ../../semantic/dist/components/button.css
[21:33:49] Created: ../../semantic/dist/components/container.min.css
[21:33:49] Created: ../../semantic/dist/components/container.css
[21:33:49] Created: ../../semantic/dist/components/divider.min.css
[21:33:49] Created: ../../semantic/dist/components/divider.css
[21:33:58] Created: ../../semantic/dist/components/emoji.min.css
[21:33:58] Created: ../../semantic/dist/components/emoji.css
[21:33:59] Created: ../../semantic/dist/components/flag.min.css
[21:33:59] Created: ../../semantic/dist/components/flag.css
[21:33:59] Created: ../../semantic/dist/components/header.min.css
[21:33:59] Created: ../../semantic/dist/components/header.css
[21:33:59] Created: ../../semantic/dist/components/icon.min.css
[21:33:59] Created: ../../semantic/dist/components/icon.css
[21:34:00] Created: ../../semantic/dist/components/image.min.css
[21:34:00] Created: ../../semantic/dist/components/image.css
[21:34:00] Created: ../../semantic/dist/components/input.min.css
[21:34:00] Created: ../../semantic/dist/components/input.css
[21:34:00] Created: ../../semantic/dist/components/label.min.css
[21:34:00] Created: ../../semantic/dist/components/label.css
[21:34:00] Created: ../../semantic/dist/components/list.min.css
[21:34:00] Created: ../../semantic/dist/components/list.css
[21:34:01] Created: ../../semantic/dist/components/loader.min.css
[21:34:01] Created: ../../semantic/dist/components/loader.css
[21:34:01] Created: ../../semantic/dist/components/placeholder.min.css
[21:34:01] Created: ../../semantic/dist/components/placeholder.css
[21:34:01] Created: ../../semantic/dist/components/rail.min.css
[21:34:01] Created: ../../semantic/dist/components/rail.css
[21:34:01] Created: ../../semantic/dist/components/reveal.min.css
[21:34:01] Created: ../../semantic/dist/components/reveal.css
[21:34:01] Created: ../../semantic/dist/components/segment.min.css
[21:34:01] Created: ../../semantic/dist/components/segment.css
[21:34:01] Created: ../../semantic/dist/components/step.min.css
[21:34:01] Created: ../../semantic/dist/components/step.css
[21:34:01] Created: ../../semantic/dist/components/text.min.css
[21:34:01] Created: ../../semantic/dist/components/text.css
[21:34:01] Created: ../../semantic/dist/components/breadcrumb.min.css
[21:34:01] Created: ../../semantic/dist/components/breadcrumb.css
[21:34:02] Created: ../../semantic/dist/components/form.min.css
[21:34:02] Created: ../../semantic/dist/components/form.css
[21:34:02] Created: ../../semantic/dist/components/grid.min.css
[21:34:02] Created: ../../semantic/dist/components/grid.css
[21:34:02] Created: ../../semantic/dist/components/menu.min.css
[21:34:02] Created: ../../semantic/dist/components/menu.css
[21:34:02] Created: ../../semantic/dist/components/message.min.css
[21:34:02] Created: ../../semantic/dist/components/message.css
[21:34:02] Created: ../../semantic/dist/components/table.min.css
[21:34:02] Created: ../../semantic/dist/components/table.css
[21:34:03] Created: ../../semantic/dist/components/ad.min.css
[21:34:03] Created: ../../semantic/dist/components/ad.css
[21:34:03] Created: ../../semantic/dist/components/card.min.css
[21:34:03] Created: ../../semantic/dist/components/card.css
[21:34:03] Created: ../../semantic/dist/components/comment.min.css
[21:34:03] Created: ../../semantic/dist/components/comment.css
[21:34:03] Created: ../../semantic/dist/components/feed.min.css
[21:34:03] Created: ../../semantic/dist/components/feed.css
[21:34:03] Created: ../../semantic/dist/components/item.min.css
[21:34:03] Created: ../../semantic/dist/components/item.css
[21:34:03] Created: ../../semantic/dist/components/statistic.min.css
[21:34:03] Created: ../../semantic/dist/components/statistic.css
[21:34:03] Created: ../../semantic/dist/components/accordion.min.css
[21:34:03] Created: ../../semantic/dist/components/accordion.css
[21:34:03] Created: ../../semantic/dist/components/calendar.min.css
[21:34:03] Created: ../../semantic/dist/components/calendar.css
[21:34:03] Created: ../../semantic/dist/components/checkbox.min.css
[21:34:03] Created: ../../semantic/dist/components/checkbox.css
[21:34:04] Created: ../../semantic/dist/components/dimmer.min.css
[21:34:04] Created: ../../semantic/dist/components/dimmer.css
[21:34:04] Created: ../../semantic/dist/components/dropdown.min.css
[21:34:04] Created: ../../semantic/dist/components/dropdown.css
[21:34:04] Created: ../../semantic/dist/components/embed.min.css
[21:34:04] Created: ../../semantic/dist/components/embed.css
[21:34:04] Created: ../../semantic/dist/components/modal.min.css
[21:34:04] Created: ../../semantic/dist/components/modal.css
[21:34:04] Created: ../../semantic/dist/components/nag.min.css
[21:34:04] Created: ../../semantic/dist/components/nag.css
[21:34:04] Created: ../../semantic/dist/components/popup.min.css
[21:34:04] Created: ../../semantic/dist/components/popup.css
[21:34:04] Created: ../../semantic/dist/components/progress.min.css
[21:34:04] Created: ../../semantic/dist/components/progress.css
[21:34:05] Created: ../../semantic/dist/components/slider.min.css
[21:34:05] Created: ../../semantic/dist/components/slider.css
[21:34:05] Created: ../../semantic/dist/components/rating.min.css
[21:34:05] Created: ../../semantic/dist/components/rating.css
[21:34:05] Created: ../../semantic/dist/components/search.min.css
[21:34:05] Created: ../../semantic/dist/components/search.css
[21:34:05] Created: ../../semantic/dist/components/shape.min.css
[21:34:05] Created: ../../semantic/dist/components/shape.css
[21:34:05] Created: ../../semantic/dist/components/sidebar.min.css
[21:34:05] Created: ../../semantic/dist/components/sidebar.css
[21:34:05] Created: ../../semantic/dist/components/sticky.min.css
[21:34:05] Created: ../../semantic/dist/components/sticky.css
[21:34:05] Created: ../../semantic/dist/components/tab.min.css
[21:34:05] Created: ../../semantic/dist/components/tab.css
[21:34:05] Created: ../../semantic/dist/components/toast.min.css
[21:34:05] Created: ../../semantic/dist/components/toast.css
[21:34:05] Created: ../../semantic/dist/components/transition.min.css
[21:34:05] Finished 'Building compressed CSS' after 19 s
[21:34:05] Created: ../../semantic/dist/components/transition.css
[21:34:05] Finished 'Building uncompressed CSS' after 19 s
[21:34:05] Starting 'Packing uncompressed CSS'...
[21:34:05] Starting 'Packing compressed CSS'...
[21:34:07] Created: ../../semantic/dist/semantic.css
[21:34:07] Finished 'Packing uncompressed CSS' after 1.33 s
[21:34:07] Created: ../../semantic/dist/semantic.min.css
[21:34:07] Finished 'Packing compressed CSS' after 1.33 s
[21:34:07] Finished 'build-css' after 20 s
[21:34:07] Starting 'build-javascript'...
[21:34:07] Starting 'Building un/compressed Javascript'...
[21:34:07] Created: ../../semantic/dist/components/form.js
[21:34:08] Created: ../../semantic/dist/components/api.js
[21:34:08] Created: ../../semantic/dist/components/form.min.js
[21:34:08] Created: ../../semantic/dist/components/state.js
[21:34:08] Created: ../../semantic/dist/components/api.min.js
[21:34:08] Created: ../../semantic/dist/components/visibility.js
[21:34:08] Created: ../../semantic/dist/components/state.min.js
[21:34:08] Created: ../../semantic/dist/components/site.js
[21:34:08] Created: ../../semantic/dist/components/visibility.min.js
[21:34:08] Created: ../../semantic/dist/components/accordion.js
[21:34:08] Created: ../../semantic/dist/components/site.min.js
[21:34:08] Created: ../../semantic/dist/components/calendar.js
[21:34:09] Created: ../../semantic/dist/components/accordion.min.js
[21:34:09] Created: ../../semantic/dist/components/checkbox.js
[21:34:09] Created: ../../semantic/dist/components/calendar.min.js
[21:34:09] Created: ../../semantic/dist/components/dimmer.js
[21:34:09] Created: ../../semantic/dist/components/checkbox.min.js
[21:34:09] Created: ../../semantic/dist/components/dropdown.js
[21:34:10] Created: ../../semantic/dist/components/dimmer.min.js
[21:34:10] Created: ../../semantic/dist/components/embed.js
[21:34:10] Created: ../../semantic/dist/components/dropdown.min.js
[21:34:10] Created: ../../semantic/dist/components/modal.js
[21:34:10] Created: ../../semantic/dist/components/embed.min.js
[21:34:10] Created: ../../semantic/dist/components/nag.js
[21:34:10] Created: ../../semantic/dist/components/modal.min.js
[21:34:10] Created: ../../semantic/dist/components/popup.js
[21:34:10] Created: ../../semantic/dist/components/nag.min.js
[21:34:10] Created: ../../semantic/dist/components/progress.js
[21:34:11] Created: ../../semantic/dist/components/popup.min.js
[21:34:11] Created: ../../semantic/dist/components/slider.js
[21:34:11] Created: ../../semantic/dist/components/progress.min.js
[21:34:11] Created: ../../semantic/dist/components/rating.js
[21:34:11] Created: ../../semantic/dist/components/slider.min.js
[21:34:11] Created: ../../semantic/dist/components/search.js
[21:34:11] Created: ../../semantic/dist/components/rating.min.js
[21:34:11] Created: ../../semantic/dist/components/shape.js
[21:34:11] Created: ../../semantic/dist/components/search.min.js
[21:34:11] Created: ../../semantic/dist/components/sidebar.js
[21:34:11] Created: ../../semantic/dist/components/shape.min.js
[21:34:11] Created: ../../semantic/dist/components/sticky.js
[21:34:11] Created: ../../semantic/dist/components/sidebar.min.js
[21:34:11] Created: ../../semantic/dist/components/tab.js
[21:34:11] Created: ../../semantic/dist/components/sticky.min.js
[21:34:11] Created: ../../semantic/dist/components/toast.js
[21:34:11] Created: ../../semantic/dist/components/tab.min.js
[21:34:11] Created: ../../semantic/dist/components/transition.js
[21:34:11] Created: ../../semantic/dist/components/toast.min.js
[21:34:11] Created: ../../semantic/dist/components/transition.min.js
[21:34:11] Finished 'Building un/compressed Javascript' after 4.65 s
[21:34:11] Starting 'Packing uncompressed Javascript'...
[21:34:11] Starting 'Packing compressed Javascript'...
[21:34:14] Created: ../../semantic/dist/semantic.js
[21:34:14] Finished 'Packing uncompressed Javascript' after 2.98 s
[21:34:14] Created: ../../semantic/dist/semantic.min.js
[21:34:14] Finished 'Packing compressed Javascript' after 2.98 s
[21:34:14] Finished 'build-javascript' after 7.63 s
[21:34:14] Starting 'build-assets'...
[21:34:14] Starting 'Building Assets'...
[21:34:14] Created: ../../semantic/dist/themes/basic/assets/fonts/icons.eot
[21:34:14] Created: ../../semantic/dist/themes/basic/assets/fonts/icons.svg
[21:34:14] Created: ../../semantic/dist/themes/basic/assets/fonts/icons.ttf
[21:34:14] Created: ../../semantic/dist/themes/basic/assets/fonts/icons.woff
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/brand-icons.eot
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/brand-icons.svg
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/brand-icons.ttf
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/brand-icons.woff
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/brand-icons.woff2
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/icons.eot
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/icons.svg
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/icons.ttf
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/icons.woff
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/icons.woff2
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/outline-icons.eot
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/outline-icons.svg
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/outline-icons.ttf
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/outline-icons.woff
[21:34:14] Created: ../../semantic/dist/themes/default/assets/fonts/outline-icons.woff2
[21:34:14] Created: ../../semantic/dist/themes/default/assets/images/flags.png
[21:34:14] Created: ../../semantic/dist/themes/github/assets/fonts/octicons-local.ttf
[21:34:14] Created: ../../semantic/dist/themes/github/assets/fonts/octicons.svg
[21:34:14] Created: ../../semantic/dist/themes/github/assets/fonts/octicons.ttf
[21:34:14] Created: ../../semantic/dist/themes/github/assets/fonts/octicons.woff
[21:34:14] Created: ../../semantic/dist/themes/material/assets/fonts/icons.eot
[21:34:14] Created: ../../semantic/dist/themes/material/assets/fonts/icons.svg
[21:34:14] Created: ../../semantic/dist/themes/material/assets/fonts/icons.ttf
[21:34:14] Created: ../../semantic/dist/themes/material/assets/fonts/icons.woff
[21:34:14] Created: ../../semantic/dist/themes/material/assets/fonts/icons.woff2
[21:34:14] Finished 'Building Assets' after 46 ms
[21:34:14] Finished 'build-assets' after 46 ms
[21:34:14] Finished 'build' after 28 s
[21:34:14] Finished 'clean up install' after 28 s
[21:34:14] Finished 'install' after 28 s

> core-js@2.6.12 postinstall /codebuild/output/src813338945/src/node_modules/core-js
> node -e "try{require('./postinstall')}catch(e){}"

Thank you for using core-js ( https://github.com/zloirock/core-js ) for polyfilling JavaScript standard library!

The project needs your help! Please consider supporting of core-js on Open Collective or Patreon:
> https://opencollective.com/core-js
> https://www.patreon.com/zloirock

Also, the author of core-js ( https://github.com/zloirock ) is looking for a good job -)

added 1091 packages in 36.692s

[Container] 2025/07/30 21:34:15.167607 Running command cd ${CODEBUILD_SRC_DIR}/_SRC/pagoda

[Container] 2025/07/30 21:34:15.174048 Running command composer install --no-dev
Composer could not detect the root package (bento/pagoda) version, defaulting to '1.0.0'. See https://getcomposer.org/root-version
Composer could not detect the root package (bento/pagoda) version, defaulting to '1.0.0'. See https://getcomposer.org/root-version
Installing dependencies from lock file
Verifying lock file contents can be installed on current platform.
Package operations: 51 installs, 0 updates, 0 removals
  - Downloading paragonie/random_compat (v9.99.100)
  - Downloading symfony/polyfill-php72 (v1.22.1)
  - Downloading symfony/polyfill-intl-normalizer (v1.22.1)
  - Downloading symfony/polyfill-intl-idn (v1.22.1)
  - Downloading ralouphie/getallheaders (3.0.3)
  - Downloading psr/http-message (1.0.1)
  - Downloading guzzlehttp/psr7 (1.8.1)
  - Downloading guzzlehttp/promises (1.4.1)
  - Downloading guzzlehttp/guzzle (6.5.5)
  - Downloading league/oauth2-client (2.6.0)
  - Downloading adam-paterson/oauth2-stripe (2.0.1)
  - Downloading bdelespierre/underscore (dev-master b64e902)
  - Downloading chriskonnertz/string-calc (v1.0.12)
  - Downloading doctrine/lexer (1.2.1)
  - Downloading drewm/mailchimp-api (v2.5.4)
  - Downloading psr/log (1.1.3)
  - Downloading react/promise (v2.8.0)
  - Downloading ezimuel/guzzlestreams (3.0.1)
  - Downloading ezimuel/ringphp (1.1.2)
  - Downloading elasticsearch/elasticsearch (v7.12.0)
  - Downloading gosquared/php-sdk (3.0.2)
  - Downloading php-http/message-factory (v1.0.2)
  - Downloading clue/stream-filter (v1.5.0)
  - Downloading php-http/message (1.11.0)
  - Downloading psr/http-client (1.0.1)
  - Downloading php-http/promise (1.1.0)
  - Downloading php-http/httplug (2.2.0)
  - Downloading php-http/discovery (1.13.0)
  - Downloading php-http/guzzle6-adapter (v2.0.2)
  - Downloading symfony/polyfill-php80 (v1.22.1)
  - Downloading symfony/polyfill-php73 (v1.22.1)
  - Downloading symfony/deprecation-contracts (v2.2.0)
  - Downloading symfony/options-resolver (v5.2.4)
  - Downloading psr/http-factory (1.0.1)
  - Downloading php-http/client-common (2.3.0)
  - Downloading intercom/intercom-php (v4.4.2)
  - Downloading mandrill/mandrill (1.0.55)
  - Downloading setasign/fpdi (v2.3.6)
  - Downloading myclabs/deep-copy (1.10.2)
  - Downloading mpdf/mpdf (v8.0.10)
  - Downloading phpseclib/phpseclib (2.0.30)
  - Downloading quickbooks/v3-php-sdk (6.0.0)
  - Downloading symfony/polyfill-mbstring (v1.22.1)
  - Downloading mtdowling/jmespath.php (2.6.0)
  - Downloading aws/aws-sdk-php (3.176.7)
  - Downloading sociallydev/spaces-api (v1)
  - Downloading stripe/stripe-php (v7.76.0)
  - Downloading symfony/polyfill-iconv (v1.22.1)
  - Downloading egulias/email-validator (3.1.1)
  - Downloading swiftmailer/swiftmailer (v6.2.7)
  - Downloading twilio/sdk (5.42.2)
  0/51 [>---------------------------]   0%
  9/51 [====>-----------------------]  17%
 21/51 [===========>----------------]  41%
 31/51 [=================>----------]  60%
 38/51 [====================>-------]  74%
 46/51 [=========================>--]  90%
 50/51 [===========================>]  98%
 51/51 [============================] 100%
  - Installing paragonie/random_compat (v9.99.100): Extracting archive
  - Installing symfony/polyfill-php72 (v1.22.1): Extracting archive
  - Installing symfony/polyfill-intl-normalizer (v1.22.1): Extracting archive
  - Installing symfony/polyfill-intl-idn (v1.22.1): Extracting archive
  - Installing ralouphie/getallheaders (3.0.3): Extracting archive
  - Installing psr/http-message (1.0.1): Extracting archive
  - Installing guzzlehttp/psr7 (1.8.1): Extracting archive
  - Installing guzzlehttp/promises (1.4.1): Extracting archive
  - Installing guzzlehttp/guzzle (6.5.5): Extracting archive
  - Installing league/oauth2-client (2.6.0): Extracting archive
  - Installing adam-paterson/oauth2-stripe (2.0.1): Extracting archive
  - Installing bdelespierre/underscore (dev-master b64e902): Extracting archive
  - Installing chriskonnertz/string-calc (v1.0.12): Extracting archive
  - Installing doctrine/lexer (1.2.1): Extracting archive
  - Installing drewm/mailchimp-api (v2.5.4): Extracting archive
  - Installing psr/log (1.1.3): Extracting archive
  - Installing react/promise (v2.8.0): Extracting archive
  - Installing ezimuel/guzzlestreams (3.0.1): Extracting archive
  - Installing ezimuel/ringphp (1.1.2): Extracting archive
  - Installing elasticsearch/elasticsearch (v7.12.0): Extracting archive
  - Installing gosquared/php-sdk (3.0.2): Extracting archive
  - Installing php-http/message-factory (v1.0.2): Extracting archive
  - Installing clue/stream-filter (v1.5.0): Extracting archive
  - Installing php-http/message (1.11.0): Extracting archive
  - Installing psr/http-client (1.0.1): Extracting archive
  - Installing php-http/promise (1.1.0): Extracting archive
  - Installing php-http/httplug (2.2.0): Extracting archive
  - Installing php-http/discovery (1.13.0): Extracting archive
  - Installing php-http/guzzle6-adapter (v2.0.2): Extracting archive
  - Installing symfony/polyfill-php80 (v1.22.1): Extracting archive
  - Installing symfony/polyfill-php73 (v1.22.1): Extracting archive
  - Installing symfony/deprecation-contracts (v2.2.0): Extracting archive
  - Installing symfony/options-resolver (v5.2.4): Extracting archive
  - Installing psr/http-factory (1.0.1): Extracting archive
  - Installing php-http/client-common (2.3.0): Extracting archive
  - Installing intercom/intercom-php (v4.4.2): Extracting archive
  - Installing mandrill/mandrill (1.0.55): Extracting archive
  - Installing setasign/fpdi (v2.3.6): Extracting archive
  - Installing myclabs/deep-copy (1.10.2): Extracting archive
  - Installing mpdf/mpdf (v8.0.10): Extracting archive
  - Installing phpseclib/phpseclib (2.0.30): Extracting archive
  - Installing quickbooks/v3-php-sdk (6.0.0): Extracting archive
  - Installing symfony/polyfill-mbstring (v1.22.1): Extracting archive
  - Installing mtdowling/jmespath.php (2.6.0): Extracting archive
  - Installing aws/aws-sdk-php (3.176.7): Extracting archive
  - Installing sociallydev/spaces-api (v1): Extracting archive
  - Installing stripe/stripe-php (v7.76.0): Extracting archive
  - Installing symfony/polyfill-iconv (v1.22.1): Extracting archive
  - Installing egulias/email-validator (3.1.1): Extracting archive
  - Installing swiftmailer/swiftmailer (v6.2.7): Extracting archive
  - Installing twilio/sdk (5.42.2): Extracting archive
  0/51 [>---------------------------]   0%
 17/51 [=========>------------------]  33%
 31/51 [=================>----------]  60%
 38/51 [====================>-------]  74%
 42/51 [=======================>----]  82%
 46/51 [=========================>--]  90%
 50/51 [===========================>]  98%
 51/51 [============================] 100%
Package mandrill/mandrill is abandoned, you should avoid using it. Use mailchimp/transactional instead.
Generating autoload files
13 packages you are using are looking for funding.
Use the `composer fund` command to find out more!

[Container] 2025/07/30 21:34:23.968536 Running command cd ${CODEBUILD_SRC_DIR}/_SRC/merge

[Container] 2025/07/30 21:34:23.974695 Running command npm ci --only=production --cache .npm
added 72 packages in 0.861s

[Container] 2025/07/30 21:34:25.154626 Running command cd ${CODEBUILD_SRC_DIR}

[Container] 2025/07/30 21:34:25.160512 Running command COMMIT_SHORT=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c1-7)

[Container] 2025/07/30 21:34:25.166587 Running command buildVersion=$COMMIT_SHORT

[Container] 2025/07/30 21:34:25.171982 Running command cd ${CODEBUILD_SRC_DIR}/_SRC/pagoda

[Container] 2025/07/30 21:34:25.177497 Running command echo "<?php const APP_BUILD = '${buildVersion}'; ?>" > BUILD_VERSION.php

[Container] 2025/07/30 21:34:25.182795 Running command cat BUILD_VERSION.php
<?php const APP_BUILD = 'f958f50'; ?>

[Container] 2025/07/30 21:34:25.188554 Running command echo "<?php const MERGE_ENDPOINT = '${MERGE_ENDPOINT}'; ?>" > BENTO_ENV.php

[Container] 2025/07/30 21:34:25.193767 Running command cat BENTO_ENV.php
<?php const MERGE_ENDPOINT = 'merge-bento.infinityhospitality.net'; ?>

[Container] 2025/07/30 21:34:25.199567 Running command cd ${CODEBUILD_SRC_DIR}/_SRC/notify/_factory

[Container] 2025/07/30 21:34:25.204872 Running command echo "var STRIPE_PK = '${STRIPE_PK}'; var CURRENT_ENV = '${CURRENT_ENV}'; var BITBUCKET_BUILD_NUMBER = '${buildVersion}'; var PAGODA_APP_VERSION = '${buildVersion}'; var PAGODA_APP_BUILD = '${buildVersion}';" > _version.js

[Container] 2025/07/30 21:34:25.210054 Running command cd ${CODEBUILD_SRC_DIR}/_SRC/merge/src

[Container] 2025/07/30 21:34:25.215517 Running command echo "var BITBUCKET_BUILD_NUMBER = '${buildVersion}'; var PAGODA_APP_VERSION = '${buildVersion}'; var PAGODA_APP_BUILD = '${buildVersion}'; module.exports = { BITBUCKET_BUILD_NUMBER, PAGODA_APP_VERSION, PAGODA_APP_BUILD };" > _version.js

[Container] 2025/07/30 21:34:25.220577 Running command cd ${CODEBUILD_SRC_DIR}

[Container] 2025/07/30 21:34:25.225699 Running command npm install gulp -g
/usr/local/bin/gulp -> /usr/local/lib/node_modules/gulp/bin/gulp.js
npm WARN optional SKIPPING OPTIONAL DEPENDENCY: fsevents@~2.3.2 (node_modules/gulp/node_modules/chokidar/node_modules/fsevents):
npm WARN notsup SKIPPING OPTIONAL DEPENDENCY: Unsupported platform for fsevents@2.3.3: wanted {"os":"darwin","arch":"any"} (current: {"os":"linux","arch":"x64"})

+ gulp@5.0.1
added 141 packages from 120 contributors in 4.922s

[Container] 2025/07/30 21:34:30.423324 Running command gulp build-bento --buildNumber $buildVersion
Browserslist: caniuse-lite is outdated. Please run:
  npx browserslist@latest --update-db
  Why you should do it regularly: https://github.com/browserslist/browserslist#browsers-data-updating
[21:34:31] Using gulpfile /codebuild/output/src813338945/src/gulpfile.js
[21:34:31] Starting 'build-bento'...
[21:34:31] Starting 'clean'...
[21:34:31] Finished 'clean' after 2.18 ms
[21:34:31] Starting 'setupSemantic'...
[21:34:31] Finished 'setupSemantic' after 8.47 ms
[21:34:31] Starting 'build-bento-app'...
[21:34:31] Starting 'start-app-production'...
[21:34:31] Starting 'start-production'...
[21:34:31] Starting 'build-production-js'...
[21:34:31] Starting 'typescript'...
[21:34:52] Finished 'typescript' after 22 s
[21:34:52] Starting 'productionJS'...
[21:35:22] Finished 'productionJS' after 30 s
[21:35:22] Starting 'productionEmbed'...
[21:35:25] Finished 'productionEmbed' after 2.57 s
[21:35:25] Starting '<anonymous>'...
[21:35:25] Finished '<anonymous>' after 416 μs
[21:35:25] Finished 'build-production-js' after 54 s
[21:35:25] Starting 'build-production-css'...
[21:35:25] Starting 'productionCSS'...
/pagoda.min-f958f50.css: 1720175
/pagoda.min-f958f50.css: 1689103
[21:35:27] Finished 'productionCSS' after 1.61 s
[21:35:27] Starting '<anonymous>'...
[21:35:27] Finished '<anonymous>' after 367 μs
[21:35:27] Finished 'build-production-css' after 1.61 s
[21:35:27] Starting '<anonymous>'...
[21:35:27] Finished '<anonymous>' after 246 μs
[21:35:27] Finished 'start-production' after 56 s
[21:35:27] Starting '<anonymous>'...
[21:35:27] Finished '<anonymous>' after 174 μs
[21:35:27] Finished 'start-app-production' after 56 s
[21:35:27] Starting '<anonymous>'...
[21:35:27] Finished '<anonymous>' after 150 μs
[21:35:27] Finished 'build-bento-app' after 56 s
[21:35:27] Starting 'move-files'...
[21:35:27] Starting 'moveFonts'...
[21:35:27] Starting 'moveIcons'...
[21:35:27] Finished 'moveIcons' after 22 ms
[21:35:27] Starting 'moveFontAwesome'...
[21:35:27] Finished 'moveFontAwesome' after 84 ms
[21:35:27] Starting '<anonymous>'...
[21:35:27] Finished '<anonymous>' after 15 ms
[21:35:27] Finished 'moveFonts' after 123 ms
[21:35:27] Starting 'moveAPIFiles'...
[21:35:44] Finished 'moveAPIFiles' after 17 s
[21:35:44] Starting 'moveMergeService'...
[21:35:45] Finished 'moveMergeService' after 940 ms
[21:35:45] Starting 'moveDatabaseMergeService'...
[21:35:45] Finished 'moveDatabaseMergeService' after 6.77 ms
[21:35:45] Starting 'moveFieldsMergeService'...
[21:35:45] Finished 'moveFieldsMergeService' after 42 ms
[21:35:45] Starting 'moveAppFiles'...
[21:35:45] Finished 'moveAppFiles' after 65 ms
[21:35:45] Starting 'moveHelpers'...
[21:35:45] Finished 'moveHelpers' after 1.56 ms
[21:35:45] Starting 'moveServiceWorkers'...
[21:35:45] Finished 'moveServiceWorkers' after 3.12 ms
[21:35:45] Starting 'moveBlueprints'...
[21:35:45] Finished 'moveBlueprints' after 113 ms
[21:35:45] Starting 'moveCronFiles'...
[21:35:45] Finished 'moveCronFiles' after 3.86 ms
[21:35:45] Starting 'moveRulesFiles'...
[21:35:45] Finished 'moveRulesFiles' after 25 ms
[21:35:45] Starting 'moveServicesFiles'...
[21:35:45] Finished 'moveServicesFiles' after 8.71 ms
[21:35:45] Starting 'moveViewsFiles'...
[21:35:45] Finished 'moveViewsFiles' after 9.43 ms
[21:35:45] Starting 'moveCaldavFiles'...
[21:35:45] Finished 'moveCaldavFiles' after 2.11 ms
[21:35:45] Starting '<anonymous>'...
[21:35:45] Finished '<anonymous>' after 16 ms
[21:35:45] Finished 'move-files' after 18 s
[21:35:45] Starting '<anonymous>'...
[21:35:45] Finished '<anonymous>' after 282 μs
[21:35:45] Finished 'build-bento' after 1.24 min

[Container] 2025/07/30 21:35:45.453028 Running command echo "BUILD_VERSION=${buildVersion}" >> build.env

[Container] 2025/07/30 21:35:45.458717 Running command echo Building the Bento Docker image...
Building the Bento Docker image...

[Container] 2025/07/30 21:35:45.464573 Running command cd ${CODEBUILD_SRC_DIR}/_SERVICES/app

[Container] 2025/07/30 21:35:45.470175 Running command echo Building tagging $REPOSITORY_URI:$COMMIT_SHORT
Building tagging ************.dkr.ecr.us-east-2.amazonaws.com/bento:f958f50

[Container] 2025/07/30 21:35:45.475542 Running command docker build --progress=plain -t $REPOSITORY_URI:$COMMIT_SHORT .
Sending build context to Docker daemon  231.8MB

Step 1/9 : FROM ************.dkr.ecr.us-east-2.amazonaws.com/bento-build:latest
latest: Pulling from bento-build
75646c2fb410: Pulling fs layer
854fb08fe050: Pulling fs layer
d099f6707d86: Pulling fs layer
038e5b090752: Pulling fs layer
56671971dcc6: Pulling fs layer
6da3e75ee2ca: Pulling fs layer
88fd46807e1d: Pulling fs layer
a969e4e5971a: Pulling fs layer
afef1d8462c6: Pulling fs layer
72d51f8e1a94: Pulling fs layer
3533d1073bc6: Pulling fs layer
0f463b6faba5: Pulling fs layer
1f258ee8562a: Pulling fs layer
7f6bfa6286ae: Pulling fs layer
4bd310d0432b: Pulling fs layer
674ba227bab5: Pulling fs layer
f586d5fb45b4: Pulling fs layer
4b6bd799c79c: Pulling fs layer
2dce55549771: Pulling fs layer
6904b922052d: Pulling fs layer
b4e74ce80299: Pulling fs layer
e569d5eabc3e: Pulling fs layer
e4ddb87b44c4: Pulling fs layer
2b2e3f437237: Pulling fs layer
9c8dca69dc95: Pulling fs layer
7f6bfa6286ae: Waiting
4bd310d0432b: Waiting
674ba227bab5: Waiting
f586d5fb45b4: Waiting
4b6bd799c79c: Waiting
2dce55549771: Waiting
6904b922052d: Waiting
b4e74ce80299: Waiting
e569d5eabc3e: Waiting
e4ddb87b44c4: Waiting
2b2e3f437237: Waiting
9c8dca69dc95: Waiting
038e5b090752: Waiting
56671971dcc6: Waiting
6da3e75ee2ca: Waiting
88fd46807e1d: Waiting
a969e4e5971a: Waiting
afef1d8462c6: Waiting
72d51f8e1a94: Waiting
3533d1073bc6: Waiting
0f463b6faba5: Waiting
1f258ee8562a: Waiting
854fb08fe050: Verifying Checksum
854fb08fe050: Download complete
038e5b090752: Verifying Checksum
038e5b090752: Download complete
56671971dcc6: Verifying Checksum
56671971dcc6: Download complete
6da3e75ee2ca: Verifying Checksum
6da3e75ee2ca: Download complete
75646c2fb410: Verifying Checksum
75646c2fb410: Download complete
88fd46807e1d: Verifying Checksum
88fd46807e1d: Download complete
afef1d8462c6: Verifying Checksum
afef1d8462c6: Download complete
a969e4e5971a: Verifying Checksum
a969e4e5971a: Download complete
72d51f8e1a94: Verifying Checksum
72d51f8e1a94: Download complete
3533d1073bc6: Verifying Checksum
3533d1073bc6: Download complete
0f463b6faba5: Verifying Checksum
0f463b6faba5: Download complete
1f258ee8562a: Verifying Checksum
1f258ee8562a: Download complete
7f6bfa6286ae: Verifying Checksum
7f6bfa6286ae: Download complete
4bd310d0432b: Verifying Checksum
4bd310d0432b: Download complete
674ba227bab5: Verifying Checksum
674ba227bab5: Download complete
d099f6707d86: Verifying Checksum
d099f6707d86: Download complete
f586d5fb45b4: Verifying Checksum
f586d5fb45b4: Download complete
6904b922052d: Verifying Checksum
6904b922052d: Download complete
75646c2fb410: Pull complete
854fb08fe050: Pull complete
b4e74ce80299: Verifying Checksum
b4e74ce80299: Download complete
2dce55549771: Verifying Checksum
2dce55549771: Download complete
e569d5eabc3e: Verifying Checksum
e569d5eabc3e: Download complete
2b2e3f437237: Verifying Checksum
2b2e3f437237: Download complete
4b6bd799c79c: Verifying Checksum
4b6bd799c79c: Download complete
9c8dca69dc95: Verifying Checksum
9c8dca69dc95: Download complete
e4ddb87b44c4: Verifying Checksum
e4ddb87b44c4: Download complete
d099f6707d86: Pull complete
038e5b090752: Pull complete
56671971dcc6: Pull complete
6da3e75ee2ca: Pull complete
88fd46807e1d: Pull complete
a969e4e5971a: Pull complete
afef1d8462c6: Pull complete
72d51f8e1a94: Pull complete
3533d1073bc6: Pull complete
0f463b6faba5: Pull complete
1f258ee8562a: Pull complete
7f6bfa6286ae: Pull complete
4bd310d0432b: Pull complete
674ba227bab5: Pull complete
f586d5fb45b4: Pull complete
4b6bd799c79c: Pull complete
2dce55549771: Pull complete
6904b922052d: Pull complete
b4e74ce80299: Pull complete
e569d5eabc3e: Pull complete
e4ddb87b44c4: Pull complete
2b2e3f437237: Pull complete
9c8dca69dc95: Pull complete
Digest: sha256:99df999e36a61afbd4904808dd61f1fe43569f3160e58335f8a8d1c2b44ac384
Status: Downloaded newer image for ************.dkr.ecr.us-east-2.amazonaws.com/bento-build:latest
 ---> 11671900b028
Step 2/9 : RUN apt-get -y --allow-releaseinfo-change update
 ---> Running in ba05c8ff9550
Ign:1 http://security.debian.org/debian-security buster/updates InRelease
Err:2 http://security.debian.org/debian-security buster/updates Release
  404  Not Found [IP: *************** 80]
Ign:3 http://deb.debian.org/debian buster InRelease
Ign:4 http://deb.debian.org/debian buster-updates InRelease
Err:5 http://deb.debian.org/debian buster Release
  404  Not Found [IP: ************** 80]
Err:6 http://deb.debian.org/debian buster-updates Release
  404  Not Found [IP: ************** 80]
Reading package lists...
E: The repository 'http://security.debian.org/debian-security buster/updates Release' no longer has a Release file.
E: The repository 'http://deb.debian.org/debian buster Release' no longer has a Release file.
E: The repository 'http://deb.debian.org/debian buster-updates Release' no longer has a Release file.
The command '/bin/sh -c apt-get -y --allow-releaseinfo-change update' returned a non-zero code: 100

[Container] 2025/07/30 21:36:01.525890 Command did not exit successfully docker build --progress=plain -t $REPOSITORY_URI:$COMMIT_SHORT . exit status 100
[Container] 2025/07/30 21:36:01.531826 Phase complete: BUILD State: FAILED
[Container] 2025/07/30 21:36:01.531841 Phase context status code: COMMAND_EXECUTION_ERROR Message: Error while executing command: docker build --progress=plain -t $REPOSITORY_URI:$COMMIT_SHORT .. Reason: exit status 100
[Container] 2025/07/30 21:36:01.621720 Entering phase POST_BUILD
[Container] 2025/07/30 21:36:01.622455 Running command echo Helm Upgrading Bento...
Helm Upgrading Bento...

[Container] 2025/07/30 21:36:01.628414 Running command cd ${CODEBUILD_SRC_DIR}/_SERVICES/helm/bento-app

[Container] 2025/07/30 21:36:01.634247 Running command echo Installing Version $REPOSITORY_URI:$COMMIT_SHORT
Installing Version ************.dkr.ecr.us-east-2.amazonaws.com/bento:f958f50

[Container] 2025/07/30 21:36:01.639686 Running command helm upgrade bentoproduction ./ --install --set config.BENTO_DATABASE_NAME=${BENTO_DATABASE_NAME} --set config.BENTO_DATABASE_PASSWORD=${BENTO_DATABASE_PASSWORD} --set config.BENTO_DATABASE_PORT=${BENTO_DATABASE_PORT} --set config.BENTO_DATABASE_SSL_FLAG=${BENTO_DATABASE_SSL_FLAG} --set config.BENTO_DATABASE_USER=${BENTO_DATABASE_USER} --set config.BENTO_DATABASE_URL=${BENTO_DATABASE_URL} --set config.BENTO_DOCS_DATABASE_NAME=${BENTO_DOCS_DATABASE_NAME} --set config.BENTO_DOCUMENTS_URL=${BENTO_DOCUMENTS_URL} --set config.DATA_ENDPOINT=${DATA_ENDPOINT} --set config.MERGE_ENDPOINT=${MERGE_ENDPOINT} --set config.ICG_APIKEY=${ICG_APIKEY} --set config.ICG_GATEWAYLIVEMODE=${ICG_GATEWAYLIVEMODE} --set config.ICG_SITEID=${ICG_SITEID} --set config.ICG_SITEKEY=${ICG_SITEKEY} --set config.STRIPE_PK=${STRIPE_PK} --set config.STRIPE_SK=${STRIPE_SK} --set config.FINIX_APK_TEST=${FINIX_APK_TEST} --set config.FINIX_APK_LIVE=${FINIX_APK_LIVE} --set config.CURRENT_ENV=${CURRENT_ENV} --set imageName=$REPOSITORY_URI --set imageTag=$COMMIT_SHORT --set config.ICG_SITEID_DREAM=${ICG_SITEID_DREAM} --set config.ICG_SITEKEY_DREAM=${ICG_SITEKEY_DREAM} --set config.ICG_APIKEY_DREAM=${ICG_APIKEY_DREAM} --set ingress.foundationGroup=${FOUNDATIONGROUP_HOST} --set resources.memory=2500Mi --set resources.cpu=2500m

Release "bentoproduction" has been upgraded. Happy Helming!
NAME: bentoproduction
LAST DEPLOYED: Wed Jul 30 21:36:02 2025
NAMESPACE: default
STATUS: deployed
REVISION: 29
TEST SUITE: None

[Container] 2025/07/30 21:36:03.547164 Running command echo Helm Upgrading Bento Merge Service...
Helm Upgrading Bento Merge Service...

[Container] 2025/07/30 21:36:03.552876 Running command cd ${CODEBUILD_SRC_DIR}/_SERVICES/helm/merge-app

[Container] 2025/07/30 21:36:03.558455 Running command echo Installing Version $REPOSITORY_URI_MERGE:$COMMIT_SHORT
Installing Version ************.dkr.ecr.us-east-2.amazonaws.com/bento-merge-service:f958f50

[Container] 2025/07/30 21:36:03.563769 Running command helm upgrade bento-merge-service ./ --install --set config.BENTO_DATABASE_NAME=${BENTO_DATABASE_NAME} --set config.BENTO_DATABASE_PASSWORD=${BENTO_DATABASE_PASSWORD} --set config.BENTO_DATABASE_PORT=${BENTO_DATABASE_PORT} --set config.BENTO_DATABASE_SSL_FLAG=${BENTO_DATABASE_SSL_FLAG} --set config.BENTO_DATABASE_USER=${BENTO_DATABASE_USER} --set config.BENTO_DATABASE_URL=${BENTO_DATABASE_URL} --set config.BENTO_DOCS_DATABASE_NAME=${BENTO_DOCS_DATABASE_NAME} --set config.BENTO_DOCUMENTS_URL=${BENTO_DOCUMENTS_URL} --set config.DATA_ENDPOINT=${DATA_ENDPOINT} --set config.MERGE_ENDPOINT=${MERGE_ENDPOINT} --set config.ICG_APIKEY=${ICG_APIKEY} --set config.ICG_GATEWAYLIVEMODE=${ICG_GATEWAYLIVEMODE} --set config.ICG_SITEID=${ICG_SITEID} --set config.ICG_SITEKEY=${ICG_SITEKEY} --set config.STRIPE_PK=${STRIPE_PK} --set config.STRIPE_SK=${STRIPE_SK} --set config.FINIX_APK_TEST=${FINIX_APK_TEST} --set config.FINIX_APK_LIVE=${FINIX_APK_LIVE} --set config.CURRENT_ENV=${CURRENT_ENV} --set imageName=$REPOSITORY_URI_MERGE --set imageTag=$COMMIT_SHORT --set config.ICG_SITEID_DREAM=${ICG_SITEID_DREAM} --set config.ICG_SITEKEY_DREAM=${ICG_SITEKEY_DREAM} --set config.ICG_APIKEY_DREAM=${ICG_APIKEY_DREAM} --set resources.memory=150m --set resources.cpu=1000Mi

W0730 21:36:04.743244    1238 warnings.go:70] spec.template.spec.containers[0].resources.limits[memory]: fractional byte value "150m" is invalid, must be an integer
W0730 21:36:04.743275    1238 warnings.go:70] spec.template.spec.containers[0].resources.requests[memory]: fractional byte value "150m" is invalid, must be an integer
Release "bento-merge-service" has been upgraded. Happy Helming!
NAME: bento-merge-service
LAST DEPLOYED: Wed Jul 30 21:36:04 2025
NAMESPACE: default
STATUS: deployed
REVISION: 29
TEST SUITE: None

[Container] 2025/07/30 21:36:04.963025 Phase complete: POST_BUILD State: SUCCEEDED
[Container] 2025/07/30 21:36:04.963040 Phase context status code:  Message:
[Container] 2025/07/30 21:36:04.999531 Set report auto-discover timeout to 5 seconds
[Container] 2025/07/30 21:36:04.999598 Expanding base directory path:  .
[Container] 2025/07/30 21:36:05.000869 Assembling file list
[Container] 2025/07/30 21:36:05.000879 Expanding .
[Container] 2025/07/30 21:36:05.002172 Expanding file paths for base directory .
[Container] 2025/07/30 21:36:05.002179 Assembling file list
[Container] 2025/07/30 21:36:05.002181 Expanding **/*
[Container] 2025/07/30 21:36:05.281656 Found 58 file(s)
[Container] 2025/07/30 21:36:05.281722 Report auto-discover file discovery took 0.282190 seconds
[Container] 2025/07/30 21:36:05.282785 Phase complete: UPLOAD_ARTIFACTS State: SUCCEEDED
[Container] 2025/07/30 21:36:05.282808 Phase context status code:  Message:
