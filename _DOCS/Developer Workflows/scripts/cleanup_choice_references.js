/**
 * Comprehensive cleanup script to fix nested choice references after running copy_categories_and_groups.js
 * 
 * This script addresses the issue where copied inventory_billable_groups AND inventory_billable_combinations
 * contain choices that still reference the original inventory_group IDs instead of the newly copied ones.
 *
 * @param {boolean} dryRun - If true, only logs changes without updating records
 * @param {number} limit - Limit processing to this many categories (for testing)
 * @param {boolean} skipNonCopied - If true, only processes items with "(*)" in name
 * @param {number} batchSize - Number of categories to process before yielding to browser (default: 5)
 * @param {boolean} processCombinations - If true, also processes inventory_billable_combinations (default: true)
 */
function cleanupChoiceReferences(dryRun = true, limit = 10, skipNonCopied = true, batchSize = 5, processCombinations = true) {
  console.log(`\n========== STARTING COMPREHENSIVE CHOICE REFERENCE CLEANUP ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will update records)'}`);
  console.log(`Processing: ${limit} categories max`);
  console.log(`Batch size: ${batchSize} categories per batch`);
  console.log(`Skip non-copied: ${skipNonCopied ? 'YES (only process items with (*) in name)' : 'NO (process all items)'}`);
  console.log(`Process combinations: ${processCombinations ? 'YES (will also process inventory_billable_combinations)' : 'NO (groups only)'}`);
  console.log(`=====================================================================\n`);

  // First, build a mapping of original group IDs to their copied counterparts
  buildGroupIdMapping(function(groupIdMap, groupsById) {
    console.log(`\nBuilt group ID mapping with ${Object.keys(groupIdMap).length} entries.`);
    
    // Log some examples of the mapping for debugging
    console.log(`\nSample group ID mappings:`);
    const sampleEntries = Object.entries(groupIdMap).slice(0, 5);
    sampleEntries.forEach(([originalId, copiedId]) => {
      const originalGroup = groupsById[originalId];
      const copiedGroup = groupsById[copiedId];
      console.log(`  ${originalId} ("${originalGroup?.name}") → ${copiedId} ("${copiedGroup?.name}")`);
    });
    
    // Process both inventory_billable_categories and inventory_billable_combination_categories
    processAllCategories(groupIdMap, groupsById, dryRun, limit, skipNonCopied, batchSize, processCombinations);
  });
}

/**
 * Process specific category IDs for targeted cleanup
 * 
 * @param {boolean} dryRun - If true, only logs changes without updating records
 * @param {number[]} categoryIds - Array of inventory_billable_categories IDs to process
 * @param {number[]} combinationCategoryIds - Array of inventory_billable_combination_categories IDs to process
 */
function cleanupSpecificCategories(dryRun = true, categoryIds = [], combinationCategoryIds = []) {
  console.log(`\n========== STARTING SPECIFIC CATEGORY CLEANUP ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will update records)'}`);
  console.log(`Target inventory_billable_categories: ${categoryIds.length > 0 ? categoryIds.join(', ') : 'none'}`);
  console.log(`Target inventory_billable_combination_categories: ${combinationCategoryIds.length > 0 ? combinationCategoryIds.join(', ') : 'none'}`);
  console.log(`========================================================\n`);

  if (categoryIds.length === 0 && combinationCategoryIds.length === 0) {
    console.log(`❌ No category IDs provided. Please specify at least one category ID.`);
    return;
  }

  // Build the group ID mapping first
  buildGroupIdMapping(function(groupIdMap, groupsById) {
    console.log(`\nBuilt group ID mapping with ${Object.keys(groupIdMap).length} entries.`);
    
    // Log some examples of the mapping for debugging
    console.log(`\nSample group ID mappings:`);
    const sampleEntries = Object.entries(groupIdMap).slice(0, 5);
    sampleEntries.forEach(([originalId, copiedId]) => {
      const originalGroup = groupsById[originalId];
      const copiedGroup = groupsById[copiedId];
      console.log(`  ${originalId} ("${originalGroup?.name}") → ${copiedId} ("${copiedGroup?.name}")`);
    });
    
    const stats = {
      categoriesProcessed: 0,
      combinationCategoriesProcessed: 0,
      groupsProcessed: 0,
      combinationsProcessed: 0,
      groupsUpdated: 0,
      combinationsUpdated: 0,
      groupsCreated: 0,
      choicesUpdated: 0,
      selectionsUpdated: 0
    };

    // Process specific inventory_billable_categories
    if (categoryIds.length > 0) {
      console.log(`\n=== PROCESSING SPECIFIC INVENTORY_BILLABLE_CATEGORIES ===`);
      processSpecificCategories(categoryIds, groupIdMap, groupsById, dryRun, stats, function() {
        
        // Process specific inventory_billable_combination_categories
        if (combinationCategoryIds.length > 0) {
          console.log(`\n=== PROCESSING SPECIFIC INVENTORY_BILLABLE_COMBINATION_CATEGORIES ===`);
          processSpecificCombinationCategories(combinationCategoryIds, groupIdMap, groupsById, dryRun, stats, function() {
            printFinalStats(stats);
          });
        } else {
          printFinalStats(stats);
        }
      });
    } else if (combinationCategoryIds.length > 0) {
      console.log(`\n=== PROCESSING SPECIFIC INVENTORY_BILLABLE_COMBINATION_CATEGORIES ===`);
      processSpecificCombinationCategories(combinationCategoryIds, groupIdMap, groupsById, dryRun, stats, function() {
        printFinalStats(stats);
      });
    }
  });
}

/**
 * Process specific inventory_billable_categories by ID
 */
function processSpecificCategories(categoryIds, groupIdMap, groupsById, dryRun, stats, callback) {
  console.log(`\nFetching specific categories: ${categoryIds.join(', ')}`);
  
  // Fetch categories by ID
  const categoriesToProcess = [];
  let fetchedCount = 0;
  
  categoryIds.forEach(categoryId => {
    databaseConnection.obj.getById('inventory_billable_categories', categoryId, function(category) {
      fetchedCount++;
      
      if (category) {
        categoriesToProcess.push(category);
        console.log(`  ✅ Found category: "${category.name}" (ID: ${category.id})`);
      } else {
        console.log(`  ❌ Category not found: ID ${categoryId}`);
      }
      
      // When all categories are fetched, process them
      if (fetchedCount === categoryIds.length) {
        if (categoriesToProcess.length > 0) {
          console.log(`\nProcessing ${categoriesToProcess.length} categories...`);
          processNextCategoryCleanup(categoriesToProcess, 0, groupIdMap, groupsById, dryRun, stats, callback);
        } else {
          console.log(`\nNo valid categories to process.`);
          if (callback) callback();
        }
      }
    });
  });
}

/**
 * Process specific inventory_billable_combination_categories by ID
 */
function processSpecificCombinationCategories(categoryIds, groupIdMap, groupsById, dryRun, stats, callback) {
  console.log(`\nFetching specific combination categories: ${categoryIds.join(', ')}`);
  
  // Fetch combination categories by ID
  const categoriesToProcess = [];
  let fetchedCount = 0;
  
  categoryIds.forEach(categoryId => {
    databaseConnection.obj.getById('inventory_billable_combination_categories', categoryId, function(category) {
      fetchedCount++;
      
      if (category) {
        categoriesToProcess.push(category);
        console.log(`  ✅ Found combination category: "${category.name}" (ID: ${category.id})`);
      } else {
        console.log(`  ❌ Combination category not found: ID ${categoryId}`);
      }
      
      // When all categories are fetched, process them
      if (fetchedCount === categoryIds.length) {
        if (categoriesToProcess.length > 0) {
          console.log(`\nProcessing ${categoriesToProcess.length} combination categories...`);
          processNextCombinationCategoryCleanup(categoriesToProcess, 0, groupIdMap, groupsById, dryRun, stats, callback);
        } else {
          console.log(`\nNo valid combination categories to process.`);
          if (callback) callback();
        }
      }
    });
  });
}

/**
 * Print final statistics
 */
function printFinalStats(stats) {
  console.log(`\n========== SPECIFIC CATEGORY CLEANUP COMPLETE ==========`);
  console.log(`Categories processed: ${stats.categoriesProcessed}`);
  console.log(`Combination categories processed: ${stats.combinationCategoriesProcessed}`);
  console.log(`Groups processed: ${stats.groupsProcessed}`);
  console.log(`Combinations processed: ${stats.combinationsProcessed}`);
  console.log(`Groups updated: ${stats.groupsUpdated}`);
  console.log(`Combinations updated: ${stats.combinationsUpdated}`);
  console.log(`Groups created: ${stats.groupsCreated}`);
  console.log(`Choices updated: ${stats.choicesUpdated}`);
  console.log(`Selections updated: ${stats.selectionsUpdated}`);
  console.log(`=======================================================\n`);
}

/**
 * Build a mapping of original group IDs to their copied counterparts
 * Also identifies groups that need to be duplicated
 */
function buildGroupIdMapping(callback) {
  console.log(`Building group ID mapping...`);
  
  databaseConnection.obj.getAll('inventory_billable_groups', function(allGroups) {
    const groupIdMap = {};
    const originalGroups = {};
    const copiedGroups = {};
    const groupsById = {};
    
    // Build lookup tables
    allGroups.forEach(group => {
      groupsById[group.id] = group;
      
      if (group.name.includes('(*)')) {
        // This is a copied group
        const originalName = group.name.replace(' (*)', '');
        if (!copiedGroups[originalName]) {
          copiedGroups[originalName] = [];
        }
        copiedGroups[originalName].push(group);
      } else {
        // This is an original group
        originalGroups[group.name] = group;
      }
    });
    
    // Create mapping from original ID to copied ID
    Object.keys(originalGroups).forEach(originalName => {
      const originalGroup = originalGroups[originalName];
      const copiedGroupArray = copiedGroups[originalName];
      
      if (copiedGroupArray && copiedGroupArray.length > 0) {
        // Sort by date_created descending to get the most recently created
        const sortedCopies = copiedGroupArray.sort((a, b) => {
          const dateA = new Date(a.date_created || '1970-01-01');
          const dateB = new Date(b.date_created || '1970-01-01');
          return dateB - dateA; // Most recent first
        });
        
        const copiedGroup = sortedCopies[0];
        groupIdMap[originalGroup.id] = copiedGroup.id;
        
        if (copiedGroupArray.length > 1) {
          console.warn(`⚠️ Multiple copied groups found for "${originalName}". Using most recent one (ID: ${copiedGroup.id}, created: ${copiedGroup.date_created}).`);
          // Log the others for reference
          sortedCopies.slice(1).forEach((other, i) => {
            console.warn(`   Other copy ${i + 1}: ID ${other.id}, created: ${other.date_created}`);
          });
        }
      }
    });
    
    console.log(`Mapping summary:`);
    console.log(`  Original groups: ${Object.keys(originalGroups).length}`);
    console.log(`  Copied groups: ${Object.keys(copiedGroups).length}`);
    console.log(`  Successful mappings: ${Object.keys(groupIdMap).length}`);
    
    callback(groupIdMap, groupsById);
  });
}

/**
 * Process both types of categories: inventory_billable_categories and inventory_billable_combination_categories
 */
function processAllCategories(groupIdMap, groupsById, dryRun, limit, skipNonCopied, batchSize, processCombinations) {
  const stats = {
    categoriesProcessed: 0,
    combinationCategoriesProcessed: 0,
    groupsProcessed: 0,
    combinationsProcessed: 0,
    groupsUpdated: 0,
    combinationsUpdated: 0,
    groupsCreated: 0,
    choicesUpdated: 0,
    selectionsUpdated: 0
  };

  console.log(`\n=== PROCESSING INVENTORY_BILLABLE_CATEGORIES ===`);
  
  // First process regular categories (inventory_billable_categories → inventory_billable_groups)
  processCategories(groupIdMap, groupsById, dryRun, limit, skipNonCopied, batchSize, stats, function() {
    
    if (processCombinations) {
      console.log(`\n=== PROCESSING INVENTORY_BILLABLE_COMBINATION_CATEGORIES ===`);
      
      // Then process combination categories (inventory_billable_combination_categories → inventory_billable_combinations)
      processCombinationCategories(groupIdMap, groupsById, dryRun, limit, skipNonCopied, batchSize, stats, function() {
        
        // Final summary
        console.log(`\n========== COMPREHENSIVE CLEANUP COMPLETE ==========`);
        console.log(`Regular categories processed: ${stats.categoriesProcessed}`);
        console.log(`Combination categories processed: ${stats.combinationCategoriesProcessed}`);
        console.log(`Groups processed: ${stats.groupsProcessed}`);
        console.log(`Combinations processed: ${stats.combinationsProcessed}`);
        console.log(`Groups updated: ${stats.groupsUpdated}`);
        console.log(`Combinations updated: ${stats.combinationsUpdated}`);
        console.log(`Groups created: ${stats.groupsCreated}`);
        console.log(`Choices updated: ${stats.choicesUpdated}`);
        console.log(`Selections updated: ${stats.selectionsUpdated}`);
        console.log(`===================================================\n`);
      });
    } else {
      // Final summary for groups only
      console.log(`\n========== CLEANUP COMPLETE (GROUPS ONLY) ==========`);
      console.log(`Categories processed: ${stats.categoriesProcessed}`);
      console.log(`Groups processed: ${stats.groupsProcessed}`);
      console.log(`Groups updated: ${stats.groupsUpdated}`);
      console.log(`Groups created: ${stats.groupsCreated}`);
      console.log(`Choices updated: ${stats.choicesUpdated}`);
      console.log(`Selections updated: ${stats.selectionsUpdated}`);
      console.log(`==================================================\n`);
    }
  });
}

/**
 * Process categories and their groups to update choice references
 */
function processCategories(groupIdMap, groupsById, dryRun, limit, skipNonCopied, batchSize, stats, callback) {
  console.log(`\nProcessing categories...`);
  
  // Get all categories (focus on copied ones if skipNonCopied is true)
  databaseConnection.obj.getAll('inventory_billable_categories', function(allCategories) {
    let categoriesToProcess = allCategories;
    
    if (skipNonCopied) {
      categoriesToProcess = allCategories.filter(cat => cat.name.includes('(*)'));
      console.log(`Filtered to ${categoriesToProcess.length} copied categories.`);
    }
    
    // Apply limit
    if (limit > 0) {
      categoriesToProcess = categoriesToProcess.slice(0, limit);
      console.log(`Limited to ${categoriesToProcess.length} categories for processing.`);
    }
    
    // Process categories in batches to avoid browser freezing
    processCategoriesInBatches(categoriesToProcess, 0, groupIdMap, groupsById, dryRun, stats, batchSize, callback);
  });
}

/**
 * Process categories in batches to avoid browser performance issues
 */
function processCategoriesInBatches(categories, startIndex, groupIdMap, groupsById, dryRun, stats, batchSize, finalCallback) {
  const endIndex = Math.min(startIndex + batchSize, categories.length);
  const currentBatch = categories.slice(startIndex, endIndex);
  
  if (currentBatch.length === 0) {
    console.log(`Completed processing all regular categories.`);
    if (finalCallback) finalCallback();
    return;
  }
  
  console.log(`\nProcessing batch ${Math.floor(startIndex / batchSize) + 1}: categories ${startIndex + 1}-${endIndex} of ${categories.length}`);
  
  // Process this batch
  processNextCategoryCleanup(currentBatch, 0, groupIdMap, groupsById, dryRun, stats, function() {
    // After this batch is complete, yield to browser and then process next batch
    setTimeout(function() {
      processCategoriesInBatches(categories, endIndex, groupIdMap, groupsById, dryRun, stats, batchSize, finalCallback);
    }, 10); // Small delay to let browser breathe
  });
}

/**
 * Process categories one at a time for cleanup
 */
function processNextCategoryCleanup(categories, index, groupIdMap, groupsById, dryRun, stats, batchCallback) {
  if (index >= categories.length) {
    console.log(`Completed processing batch of ${categories.length} categories.`);
    if (batchCallback) batchCallback();
    return;
  }
  
  const category = categories[index];
  stats.categoriesProcessed++;
  
  console.log(`\n========== CATEGORY ${index + 1}/${categories.length} ==========`);
  console.log(`Processing category: "${category.name}" (ID: ${category.id})`);
  
  // Get all groups for this category
  databaseConnection.obj.getWhere('inventory_billable_groups', {category: category.id}, function(groups) {
    console.log(`Found ${groups.length} groups in this category.`);
    
    if (groups.length === 0) {
      // No groups to process, move to next category
      processNextCategoryCleanup(categories, index + 1, groupIdMap, groupsById, dryRun, stats, batchCallback);
      return;
    }
    
    // Process groups for this category
    processNextGroupCleanup(groups, 0, groupIdMap, groupsById, dryRun, stats, function() {
      // Move to next category
      processNextCategoryCleanup(categories, index + 1, groupIdMap, groupsById, dryRun, stats, batchCallback);
    });
  });
}

/**
 * Process groups one at a time for cleanup
 */
function processNextGroupCleanup(groups, index, groupIdMap, groupsById, dryRun, stats, callback) {
  if (index >= groups.length) {
    console.log(`Completed processing all groups for this category.`);
    if (callback) callback();
    return;
  }
  
  const group = groups[index];
  stats.groupsProcessed++;
  
  console.log(`\nProcessing group: "${group.name}" (ID: ${group.id})`);
  
  let groupNeedsUpdate = false;
  let updatedGroup = JSON.parse(JSON.stringify(group)); // Deep clone
  const missingGroups = []; // Track groups that need to be created
  
  // Check if this group has items with choices that need updating
  if (updatedGroup.items && Array.isArray(updatedGroup.items)) {
    console.log(`  Group has ${updatedGroup.items.length} items.`);
    
    updatedGroup.items.forEach((item, itemIndex) => {
      if (item.choices && Array.isArray(item.choices)) {
        console.log(`    Item ${itemIndex + 1} has ${item.choices.length} choices.`);
        
        item.choices.forEach((choice, choiceIndex) => {
          if (choice.inventory_group) {
            if (groupIdMap[choice.inventory_group]) {
              // We have a mapping, update the reference
              const oldInventoryGroupId = choice.inventory_group;
              const newInventoryGroupId = groupIdMap[choice.inventory_group];
              
              console.log(`      Updating choice ${choiceIndex + 1}: inventory_group ${oldInventoryGroupId} → ${newInventoryGroupId}`);
              
              choice.inventory_group = newInventoryGroupId;
              groupNeedsUpdate = true;
              stats.choicesUpdated++;
            } else {
              // Check if this referenced group exists and needs to be duplicated
              const referencedGroup = groupsById[choice.inventory_group];
              if (referencedGroup && !referencedGroup.name.includes('(*)')) {
                console.log(`      ⚠️ Choice ${choiceIndex + 1} references unduplicated group: "${referencedGroup.name}" (ID: ${referencedGroup.id})`);
                
                // Add to missing groups list if not already there
                if (!missingGroups.find(mg => mg.id === referencedGroup.id)) {
                  missingGroups.push(referencedGroup);
                }
              } else if (!referencedGroup) {
                console.log(`      ❌ Choice ${choiceIndex + 1} references non-existent group ID: ${choice.inventory_group}`);
              }
            }
          }
        });
      }
    });
  }
  
  // Check if this group has selections that need updating
  if (updatedGroup.selection && Array.isArray(updatedGroup.selection)) {
    console.log(`  Group has ${updatedGroup.selection.length} selections.`);
    
    // Note: The selection structure is more complex and may need deeper analysis
    // For now, we'll log what we find but not modify it automatically
    updatedGroup.selection.forEach((selection, selectionIndex) => {
      console.log(`    Selection ${selectionIndex + 1}: ${JSON.stringify(selection).substring(0, 100)}...`);
    });
  }
  
  // If we found missing groups, create them first
  if (missingGroups.length > 0) {
    console.log(`  📋 Found ${missingGroups.length} groups that need to be duplicated:`);
    missingGroups.forEach((mg, i) => {
      console.log(`    ${i + 1}. "${mg.name}" (ID: ${mg.id})`);
    });
    
    // Create missing groups first, then update the current group
    createMissingGroups(missingGroups, 0, groupIdMap, dryRun, stats, function() {
      // Now process the current group again with updated mapping
      processCurrentGroupWithUpdatedMapping(group, updatedGroup, groupNeedsUpdate, groupIdMap, dryRun, stats, function() {
        // Process next group after a short delay
        setTimeout(function() {
          processNextGroupCleanup(groups, index + 1, groupIdMap, groupsById, dryRun, stats, callback);
        }, 100);
      });
    });
  } else {
    // No missing groups, proceed with updating current group
    processCurrentGroupWithUpdatedMapping(group, updatedGroup, groupNeedsUpdate, groupIdMap, dryRun, stats, function() {
      // Process next group (no delay needed)
      processNextGroupCleanup(groups, index + 1, groupIdMap, groupsById, dryRun, stats, callback);
    });
  }
}

/**
 * Create missing groups (those referenced but not duplicated)
 */
function createMissingGroups(missingGroups, index, groupIdMap, dryRun, stats, callback) {
  if (index >= missingGroups.length) {
    console.log(`  ✅ Completed creating missing groups.`);
    if (callback) callback();
    return;
  }
  
  const originalGroup = missingGroups[index];
  
  console.log(`  📝 Creating copy of group: "${originalGroup.name}" (ID: ${originalGroup.id})`);
  
  if (!dryRun) {
    // Create the new group following the same pattern as the original script
    const newGroup = _.clone(originalGroup);
    
    // Remove the id property as a new one will be assigned by the database
    delete newGroup.id;
    
    // Update name by appending "(*)"
    newGroup.name = originalGroup.name + ' (*)';
    
    // Remove surcharges with ID 1310026 if it exists
    if (newGroup.surcharges && Array.isArray(newGroup.surcharges)) {
      const originalLength = newGroup.surcharges.length;
      newGroup.surcharges = newGroup.surcharges.filter(id => id !== 1310026);
      console.log(`    Modified surcharges: ${originalLength} → ${newGroup.surcharges.length} (Removed 1310026)`);
    }
    
    // Update date_created to current time
    newGroup.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');
    
    databaseConnection.obj.create('inventory_billable_groups', newGroup, function(createdGroup) {
      console.log(`    ✅ Created group "${createdGroup.name}" (ID: ${createdGroup.id})`);
      
      // Add to mapping
      groupIdMap[originalGroup.id] = createdGroup.id;
      stats.groupsCreated++;
      
      // Process next missing group after a short delay
      setTimeout(function() {
        createMissingGroups(missingGroups, index + 1, groupIdMap, dryRun, stats, callback);
      }, 100);
    });
  } else {
    console.log(`    [DRY RUN] Would create group "${originalGroup.name + ' (*)'}" for original ID ${originalGroup.id}`);
    
    // For dry run, simulate the mapping update
    groupIdMap[originalGroup.id] = `new-id-${originalGroup.id}`;
    
    // Process next missing group (no delay needed for dry run)
    createMissingGroups(missingGroups, index + 1, groupIdMap, dryRun, stats, callback);
  }
}

/**
 * Process the current group with updated mapping
 */
function processCurrentGroupWithUpdatedMapping(originalGroup, updatedGroup, groupNeedsUpdate, groupIdMap, dryRun, stats, callback) {
  // Re-scan the choices with the updated mapping
  if (updatedGroup.items && Array.isArray(updatedGroup.items)) {
    updatedGroup.items.forEach((item, itemIndex) => {
      if (item.choices && Array.isArray(item.choices)) {
        item.choices.forEach((choice, choiceIndex) => {
          if (choice.inventory_group && groupIdMap[choice.inventory_group]) {
            const oldInventoryGroupId = choice.inventory_group;
            const newInventoryGroupId = groupIdMap[choice.inventory_group];
            
            if (oldInventoryGroupId !== newInventoryGroupId) {
              console.log(`      Final update choice ${choiceIndex + 1}: inventory_group ${oldInventoryGroupId} → ${newInventoryGroupId}`);
              
              choice.inventory_group = newInventoryGroupId;
              groupNeedsUpdate = true;
              stats.choicesUpdated++;
            }
          }
        });
      }
    });
  }
  
  // Update the group if needed
  if (groupNeedsUpdate) {
    stats.groupsUpdated++;
    
    if (!dryRun) {
      console.log(`  ✅ UPDATING group "${originalGroup.name}" (ID: ${originalGroup.id})`);
      
      databaseConnection.obj.update('inventory_billable_groups', originalGroup.id, updatedGroup, function(updatedResult) {
        console.log(`  ✅ Successfully updated group "${originalGroup.name}"`);
        if (callback) callback();
      });
    } else {
      console.log(`  [DRY RUN] Would update group "${originalGroup.name}" (ID: ${originalGroup.id})`);
      if (callback) callback();
    }
  } else {
    console.log(`  ℹ️ No updates needed for group "${originalGroup.name}"`);
    if (callback) callback();
  }
}

/**
 * Process combination categories and their combinations to update choice references
 */
function processCombinationCategories(groupIdMap, groupsById, dryRun, limit, skipNonCopied, batchSize, stats, callback) {
  console.log(`\nProcessing combination categories...`);
  
  // Get all combination categories (focus on copied ones if skipNonCopied is true)
  databaseConnection.obj.getAll('inventory_billable_combination_categories', function(allCategories) {
    let categoriesToProcess = allCategories;
    
    if (skipNonCopied) {
      categoriesToProcess = allCategories.filter(cat => cat.name.includes('(*)'));
      console.log(`Filtered to ${categoriesToProcess.length} copied combination categories.`);
    }
    
    // Apply limit
    if (limit > 0) {
      categoriesToProcess = categoriesToProcess.slice(0, limit);
      console.log(`Limited to ${categoriesToProcess.length} combination categories for processing.`);
    }
    
    // Process combination categories in batches to avoid browser freezing
    processCombinationCategoriesInBatches(categoriesToProcess, 0, groupIdMap, groupsById, dryRun, stats, batchSize, callback);
  });
}

/**
 * Process combination categories in batches to avoid browser performance issues
 */
function processCombinationCategoriesInBatches(categories, startIndex, groupIdMap, groupsById, dryRun, stats, batchSize, finalCallback) {
  const endIndex = Math.min(startIndex + batchSize, categories.length);
  const currentBatch = categories.slice(startIndex, endIndex);
  
  if (currentBatch.length === 0) {
    console.log(`Completed processing all combination categories.`);
    if (finalCallback) finalCallback();
    return;
  }
  
  console.log(`\nProcessing combination batch ${Math.floor(startIndex / batchSize) + 1}: categories ${startIndex + 1}-${endIndex} of ${categories.length}`);
  
  // Process this batch
  processNextCombinationCategoryCleanup(currentBatch, 0, groupIdMap, groupsById, dryRun, stats, function() {
    // After this batch is complete, yield to browser and then process next batch
    setTimeout(function() {
      processCombinationCategoriesInBatches(categories, endIndex, groupIdMap, groupsById, dryRun, stats, batchSize, finalCallback);
    }, 10); // Small delay to let browser breathe
  });
}

/**
 * Process combination categories one at a time for cleanup
 */
function processNextCombinationCategoryCleanup(categories, index, groupIdMap, groupsById, dryRun, stats, batchCallback) {
  if (index >= categories.length) {
    console.log(`Completed processing batch of ${categories.length} combination categories.`);
    if (batchCallback) batchCallback();
    return;
  }
  
  const category = categories[index];
  stats.combinationCategoriesProcessed++;
  
  console.log(`\n========== COMBINATION CATEGORY ${index + 1}/${categories.length} ==========`);
  console.log(`Processing combination category: "${category.name}" (ID: ${category.id})`);
  
  // Get all combinations for this category
  databaseConnection.obj.getWhere('inventory_billable_combinations', {category: category.id}, function(combinations) {
    console.log(`Found ${combinations.length} combinations in this category.`);
    
    if (combinations.length === 0) {
      // No combinations to process, move to next category
      processNextCombinationCategoryCleanup(categories, index + 1, groupIdMap, groupsById, dryRun, stats, batchCallback);
      return;
    }
    
    // Process combinations for this category
    processNextCombinationCleanup(combinations, 0, groupIdMap, groupsById, dryRun, stats, function() {
      // Move to next category
      processNextCombinationCategoryCleanup(categories, index + 1, groupIdMap, groupsById, dryRun, stats, batchCallback);
    });
  });
}

/**
 * Process combinations one at a time for cleanup
 */
function processNextCombinationCleanup(combinations, index, groupIdMap, groupsById, dryRun, stats, callback) {
  if (index >= combinations.length) {
    console.log(`Completed processing all combinations for this category.`);
    if (callback) callback();
    return;
  }
  
  const combination = combinations[index];
  stats.combinationsProcessed++;
  
  console.log(`\nProcessing combination: "${combination.name}" (ID: ${combination.id})`);
  
  // Get full combination details (combinations may have nested structure like groups)
  databaseConnection.obj.getById('inventory_billable_combinations', combination.id, function(fullCombination) {
    if (!fullCombination) {
      console.log(`  ❌ Could not retrieve full combination details for ID: ${combination.id}`);
      processNextCombinationCleanup(combinations, index + 1, groupIdMap, groupsById, dryRun, stats, callback);
      return;
    }
    
    let combinationNeedsUpdate = false;
    let updatedCombination = JSON.parse(JSON.stringify(fullCombination)); // Deep clone
    const missingGroups = []; // Track groups that need to be created
    
    // Check if this combination has items with choices that need updating
    // NOTE: This assumes combinations have similar structure to groups
    // You may need to adjust this based on the actual structure
    if (updatedCombination.items && Array.isArray(updatedCombination.items)) {
      console.log(`  Combination has ${updatedCombination.items.length} items.`);
      
      updatedCombination.items.forEach((item, itemIndex) => {
        if (item.choices && Array.isArray(item.choices)) {
          console.log(`    Item ${itemIndex + 1} has ${item.choices.length} choices.`);
          
          item.choices.forEach((choice, choiceIndex) => {
            if (choice.inventory_group) {
              if (groupIdMap[choice.inventory_group]) {
                // We have a mapping, update the reference
                const oldInventoryGroupId = choice.inventory_group;
                const newInventoryGroupId = groupIdMap[choice.inventory_group];
                
                console.log(`      Updating choice ${choiceIndex + 1}: inventory_group ${oldInventoryGroupId} → ${newInventoryGroupId}`);
                
                choice.inventory_group = newInventoryGroupId;
                combinationNeedsUpdate = true;
                stats.choicesUpdated++;
              } else {
                // Check if this referenced group exists and needs to be duplicated
                const referencedGroup = groupsById[choice.inventory_group];
                if (referencedGroup && !referencedGroup.name.includes('(*)')) {
                  console.log(`      ⚠️ Choice ${choiceIndex + 1} references unduplicated group: "${referencedGroup.name}" (ID: ${referencedGroup.id})`);
                  
                  // Add to missing groups list if not already there
                  if (!missingGroups.find(mg => mg.id === referencedGroup.id)) {
                    missingGroups.push(referencedGroup);
                  }
                } else if (!referencedGroup) {
                  console.log(`      ❌ Choice ${choiceIndex + 1} references non-existent group ID: ${choice.inventory_group}`);
                }
              }
            }
          });
        }
      });
    }
    
    // If we found missing groups, create them first
    if (missingGroups.length > 0) {
      console.log(`  📋 Found ${missingGroups.length} groups that need to be duplicated for combination:`);
      missingGroups.forEach((mg, i) => {
        console.log(`    ${i + 1}. "${mg.name}" (ID: ${mg.id})`);
      });
      
      // Create missing groups first, then update the current combination
      createMissingGroups(missingGroups, 0, groupIdMap, dryRun, stats, function() {
        // Now process the current combination again with updated mapping
        processCurrentCombinationWithUpdatedMapping(fullCombination, updatedCombination, combinationNeedsUpdate, groupIdMap, dryRun, stats, function() {
          // Process next combination after a short delay
          setTimeout(function() {
            processNextCombinationCleanup(combinations, index + 1, groupIdMap, groupsById, dryRun, stats, callback);
          }, 100);
        });
      });
    } else {
      // No missing groups, proceed with updating current combination
      processCurrentCombinationWithUpdatedMapping(fullCombination, updatedCombination, combinationNeedsUpdate, groupIdMap, dryRun, stats, function() {
        // Process next combination (no delay needed)
        processNextCombinationCleanup(combinations, index + 1, groupIdMap, groupsById, dryRun, stats, callback);
      });
    }
  });
}

/**
 * Process the current combination with updated mapping
 */
function processCurrentCombinationWithUpdatedMapping(originalCombination, updatedCombination, combinationNeedsUpdate, groupIdMap, dryRun, stats, callback) {
  // Re-scan the choices with the updated mapping
  if (updatedCombination.items && Array.isArray(updatedCombination.items)) {
    updatedCombination.items.forEach((item, itemIndex) => {
      if (item.choices && Array.isArray(item.choices)) {
        item.choices.forEach((choice, choiceIndex) => {
          if (choice.inventory_group && groupIdMap[choice.inventory_group]) {
            const oldInventoryGroupId = choice.inventory_group;
            const newInventoryGroupId = groupIdMap[choice.inventory_group];
            
            if (oldInventoryGroupId !== newInventoryGroupId) {
              console.log(`      Final update choice ${choiceIndex + 1}: inventory_group ${oldInventoryGroupId} → ${newInventoryGroupId}`);
              
              choice.inventory_group = newInventoryGroupId;
              combinationNeedsUpdate = true;
              stats.choicesUpdated++;
            }
          }
        });
      }
    });
  }
  
  // Update the combination if needed
  if (combinationNeedsUpdate) {
    stats.combinationsUpdated++;
    
    if (!dryRun) {
      console.log(`  ✅ UPDATING combination "${originalCombination.name}" (ID: ${originalCombination.id})`);
      
      databaseConnection.obj.update('inventory_billable_combinations', originalCombination.id, updatedCombination, function(updatedResult) {
        console.log(`  ✅ Successfully updated combination "${originalCombination.name}"`);
        if (callback) callback();
      });
    } else {
      console.log(`  [DRY RUN] Would update combination "${originalCombination.name}" (ID: ${originalCombination.id})`);
      if (callback) callback();
    }
  } else {
    console.log(`  ℹ️ No updates needed for combination "${originalCombination.name}"`);
    if (callback) callback();
  }
}

/**
 * Additional helper function to find and report orphaned references
 */
function findOrphanedReferences(dryRun = true) {
  console.log(`\n========== FINDING ORPHANED REFERENCES ==========`);
  
  databaseConnection.obj.getAll('inventory_billable_groups', function(allGroups) {
    const existingGroupIds = new Set(allGroups.map(g => g.id));
    const orphanedReferences = [];
    
    allGroups.forEach(group => {
      if (group.items && Array.isArray(group.items)) {
        group.items.forEach((item, itemIndex) => {
          if (item.choices && Array.isArray(item.choices)) {
            item.choices.forEach((choice, choiceIndex) => {
              if (choice.inventory_group && !existingGroupIds.has(choice.inventory_group)) {
                orphanedReferences.push({
                  groupId: group.id,
                  groupName: group.name,
                  itemIndex: itemIndex,
                  choiceIndex: choiceIndex,
                  orphanedInventoryGroupId: choice.inventory_group
                });
              }
            });
          }
        });
      }
    });
    
    console.log(`Found ${orphanedReferences.length} orphaned references:`);
    orphanedReferences.forEach((ref, index) => {
      console.log(`  ${index + 1}. Group "${ref.groupName}" (ID: ${ref.groupId})`);
      console.log(`     Item ${ref.itemIndex + 1}, Choice ${ref.choiceIndex + 1}`);
      console.log(`     References non-existent inventory_group: ${ref.orphanedInventoryGroupId}`);
    });
    
    console.log(`================================================\n`);
  });
}

/**
 * Utility function to analyze the structure of a specific group
 */
function analyzeGroupStructure(groupId) {
  console.log(`\n========== ANALYZING GROUP STRUCTURE ==========`);
  
  databaseConnection.obj.getById('inventory_billable_groups', groupId, function(group) {
    if (!group) {
      console.log(`Group with ID ${groupId} not found.`);
      return;
    }
    
    console.log(`Group: "${group.name}" (ID: ${group.id})`);
    console.log(`Category: ${group.category}`);
    
    if (group.items && Array.isArray(group.items)) {
      console.log(`\nItems (${group.items.length}):`);
      group.items.forEach((item, itemIndex) => {
        console.log(`  Item ${itemIndex + 1}: "${item.name}" (ID: ${item.id})`);
        console.log(`    Inventory Group: ${item.inventory_group || 'None'}`);
        console.log(`    Max Selections: ${item.max_selections || 'None'}`);
        
        if (item.choices && Array.isArray(item.choices)) {
          console.log(`    Choices (${item.choices.length}):`);
          item.choices.forEach((choice, choiceIndex) => {
            console.log(`      Choice ${choiceIndex + 1}: "${choice.name}" (ID: ${choice.id})`);
            console.log(`        Inventory Group: ${choice.inventory_group || 'None'}`);
            console.log(`        Additional Price: ${choice.additional_price || 0}`);
          });
        }
      });
    }
    
    if (group.selection && Array.isArray(group.selection)) {
      console.log(`\nSelections (${group.selection.length}):`);
      group.selection.forEach((selection, selectionIndex) => {
        console.log(`  Selection ${selectionIndex + 1}:`);
        console.log(`    Ingredient Key: ${selection.ingr_key || 'None'}`);
        console.log(`    Choices: ${JSON.stringify(selection.choice || [])}`);
      });
    }
    
    console.log(`===============================================\n`);
  });
}