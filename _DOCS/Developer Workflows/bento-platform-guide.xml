<?xml version="1.0" encoding="UTF-8"?>
<BentoPlatform version="2.0" updated="2025-07-28">

  <!--
    BENTO PLATFORM COMPREHENSIVE REFERENCE

    This document serves as the definitive guide to the Bento application framework,
    a comprehensive ERP system for SMBs built with hybrid PHP/Node.js microservices architecture.

    Use this document for AI context setting sessions to understand:
    - Core architectural patterns and components
    - Multi-tenant database structure and operations
    - Frontend framework (Notify) and backend API (Pagoda)
    - Document processing and merge services
    - Development workflows and best practices
  -->

  <Architecture>
    <Overview>
      <Description>
        Bento Systems is a comprehensive ERP application for SMBs built with a hybrid
        PHP/Node.js microservices architecture designed for multi-tenant operation.
      </Description>
      <Components>
        <Component>
          <Name>Backend (Pagoda)</Name>
          <Location>_SRC/pagoda/</Location>
          <Technology>PHP</Technology>
          <Purpose>API handling CRUD operations, authentication, business logic, and integrations</Purpose>
        </Component>
        <Component>
          <Name>Frontend (Notify)</Name>
          <Location>_SRC/notify/</Location>
          <Technology>Custom JavaScript Framework</Technology>
          <Purpose>Component-based UI framework with Factory/Sandbox pattern</Purpose>
        </Component>
        <Component>
          <Name>Merge Service</Name>
          <Location>_SRC/merge/</Location>
          <Technology>Node.js</Technology>
          <Purpose>Document template processing using Mustache templating</Purpose>
        </Component>
        <Component>
          <Name>Database Layer</Name>
          <Technology>PostgreSQL</Technology>
          <Purpose>Dual database setup - main (port 5432) and documents (port 5434)</Purpose>
        </Component>
      </Components>
    </Overview>

    <MultiTenantArchitecture>
      <Description>
        Bento operates as a multi-tenant system where 'voltzsoftware' serves as the main
        infrastructure account, while individual app instances use instance-specific
        database keys and configurations.
      </Description>
      <InstanceConfiguration>
        <MainInstance>voltzsoftware</MainInstance>
        <DevelopmentInstances>
          <Instance>rickyvoltz</Instance>
          <Instance>infinity</Instance>
          <Instance>dreamcatering</Instance>
        </DevelopmentInstances>
        <ConfigurationPattern>
          <![CDATA[
            // Instance detection from URL or API key
            $instanceName = str_replace('/', '', str_replace('/app/', '', $_REQUEST['pagodaAPIKey']));

            // Database connection per instance
            $pgObjects = new pgObjects($pdo, $instanceName, $rules);
          ]]>
        </ConfigurationPattern>
      </InstanceConfiguration>
    </MultiTenantArchitecture>
  </Architecture>

  <DatabaseArchitecture>
    <Structure>
      <Description>
        Bento uses a unified object storage approach where all application data is stored
        in a single 'objects' table using JSONB columns, with object_bp_type field
        distinguishing between different object types.
      </Description>
      <MainTable>
        <Name>objects</Name>
        <Schema>
          <![CDATA[
            CREATE TABLE objects (
              id SERIAL PRIMARY KEY,
              instance TEXT,
              object_type TEXT,
              object_data JSONB,
              date_created TIMESTAMP DEFAULT NOW(),
              is_deleted BOOLEAN DEFAULT FALSE,
              tagged_with INTEGER[],
              shared_with INTEGER[],
              read_obj INTEGER[],
              write_obj INTEGER[],
              notify INTEGER[]
            );
          ]]>
        </Schema>
      </MainTable>
      <ObjectTypeSystem>
        <Description>
          Objects are differentiated by the object_bp_type field within the JSONB data,
          allowing for flexible schema evolution while maintaining type safety.
        </Description>
        <CommonObjectTypes>
          <ObjectType>contacts</ObjectType>
          <ObjectType>companies</ObjectType>
          <ObjectType>projects</ObjectType>
          <ObjectType>contracts</ObjectType>
          <ObjectType>invoices</ObjectType>
          <ObjectType>inventory_billable_combinations</ObjectType>
          <ObjectType>inventory_billable_groups</ObjectType>
          <ObjectType>inventory_billable_combination_categories</ObjectType>
        </CommonObjectTypes>
      </ObjectTypeSystem>
    </Structure>

    <DataOperations>
      <Description>Database operations follow consistent patterns across the application</Description>
      <StandardOperations>
        <Operation>
          <Name>Create</Name>
          <Signature>databaseConnection.obj.create(objectType, objectData, callback)</Signature>
          <Description>Creates new objects with auto-generated IDs</Description>
        </Operation>
        <Operation>
          <Name>Update</Name>
          <Signature>databaseConnection.obj.update(objectType, objectWithId, callback)</Signature>
          <Description>Updates existing objects - requires exactly 3 arguments</Description>
        </Operation>
        <Operation>
          <Name>GetById</Name>
          <Signature>databaseConnection.obj.getById(objectType, id, callback)</Signature>
          <Description>Retrieves single object by ID</Description>
        </Operation>
        <Operation>
          <Name>GetWhere</Name>
          <Signature>databaseConnection.obj.getWhere(objectType, queryObj, callback)</Signature>
          <Description>Queries objects with conditions</Description>
        </Operation>
        <Operation>
          <Name>Erase</Name>
          <Signature>databaseConnection.obj.erase(id, callback)</Signature>
          <Description>Soft deletes objects by ID</Description>
        </Operation>
      </StandardOperations>
    </Structure>
  </DatabaseArchitecture>

  <CoreConcepts>
    <Concept>
      <Name>Factory and Module Registration</Name>
      <Description>
        The Factory is the central registry for all Bento modules. Components register themselves with the Factory to become available in the system.
      </Description>
      <Example>
        <![CDATA[
          Factory.register('componentName', function (sb) {
            // Module implementation

            return {
              // Public API
              init: function() {
                // Initialize the module
              }
            };
          });
        ]]>
      </Example>
      <Notes>
        <Note>Every module receives a sandbox (sb) parameter for controlled access to the core system</Note>
        <Note>Modules must return an object with at least an init() method</Note>
        <Note>Factory.register is the entry point for all components in the system</Note>
      </Notes>
    </Concept>

    <Concept>
      <Name>Sandbox (sb)</Name>
      <Description>
        The sandbox is a mediator that provides controlled access to the core system. It prevents modules from directly accessing other modules or system internals.
      </Description>
      <CommonMethods>
        <Method>
          <Name>sb.notify</Name>
          <Description>Publishes an event to the system's message bus</Description>
          <Signature>sb.notify({ type: 'event-name', data: {...} })</Signature>
          <Example>
            <![CDATA[
              sb.notify({
                type: 'register-application',
                data: {
                  navigationItem: {
                    id: 'componentId',
                    title: 'Component Title'
                    // Other properties
                  }
                }
              });
            ]]>
          </Example>
        </Method>
        <Method>
          <Name>sb.listen</Name>
          <Description>Subscribes to system events</Description>
          <Signature>sb.listen({ 'event-name': callbackFunction })</Signature>
          <Example>
            <![CDATA[
              var listeners = {
                'custom-event': this.handleCustomEvent,
                'another-event': this.handleAnotherEvent
              };

              sb.listen(listeners);
            ]]>
          </Example>
        </Method>
        <Method>
          <Name>sb.data</Name>
          <Description>Access to data operations and utilities</Description>
          <SubMethods>
            <SubMethod>
              <Name>sb.data.db.obj</Name>
              <Description>Database object operations</Description>
              <Operations>
                <Operation>getById(type, id, callback)</Operation>
                <Operation>getWhere(type, conditions, callback, fields)</Operation>
                <Operation>getAll(type, callback, fields)</Operation>
                <Operation>create(type, data, callback)</Operation>
                <Operation>update(type, id, data, callback)</Operation>
              </Operations>
            </SubMethod>
            <SubMethod>
              <Name>sb.data.url</Name>
              <Description>URL creation utilities</Description>
              <Operations>
                <Operation>createPageURL(type, params)</Operation>
              </Operations>
            </SubMethod>
            <SubMethod>
              <Name>sb.data.makePDF</Name>
              <Description>Generate PDFs from HTML</Description>
              <Signature>sb.data.makePDF(htmlContent, mode)</Signature>
              <Modes>
                <Mode>I: Display inline</Mode>
                <Mode>D: Download</Mode>
                <Mode>F: Save to server file</Mode>
                <Mode>S: Return as string</Mode>
              </Modes>
            </SubMethod>
          </SubMethods>
        </Method>
      </CommonMethods>
    </Concept>

    <Concept>
      <Name>DOM Tools</Name>
      <Description>
        Bento provides a DOM manipulation abstraction layer for creating and managing UI elements.
      </Description>
      <Example>
        <![CDATA[
          // Create a container
          ui.makeNode('container', 'div', {
            css: 'ui segment',
            text: 'Some content'
          });

          // Add a child element
          ui.container.makeNode('title', 'div', {
            css: 'ui header',
            tag: 'h2',
            text: 'Title Text'
          });

          // Apply changes
          ui.patch();
        ]]>
      </Example>
      <CommonOperations>
        <Operation>
          <Name>makeNode(id, type, options)</Name>
          <Description>Creates a new DOM node</Description>
          <Parameters>
            <Parameter>id: String identifier for the node</Parameter>
            <Parameter>type: Type of node ('div', 'lineBreak', custom types)</Parameter>
            <Parameter>options: Object with properties (css, tag, text, style, etc.)</Parameter>
          </Parameters>
        </Operation>
        <Operation>
          <Name>empty()</Name>
          <Description>Clears all content from a node</Description>
        </Operation>
        <Operation>
          <Name>patch()</Name>
          <Description>Applies all pending DOM changes</Description>
        </Operation>
      </CommonOperations>
      <SpecialNodeTypes>
        <NodeType>div: Standard container</NodeType>
        <NodeType>lineBreak: Creates spacing</NodeType>
        <NodeType>Custom factory types (defined in _factory/domTools)</NodeType>
      </SpecialNodeTypes>
    </Concept>

    <Concept>
      <Name>Event System</Name>
      <Description>
        Bento uses a publish/subscribe event system for communication between modules.
      </Description>
      <EventFlow>
        <Step>1. Modules register event listeners using sb.listen()</Step>
        <Step>2. Events are published using sb.notify()</Step>
        <Step>3. Event data is passed to registered handlers</Step>
      </EventFlow>
      <CommonEvents>
        <Event>
          <Name>register-application</Name>
          <Description>Registers a component in the navigation system</Description>
        </Event>
        <Event>
          <Name>register-report</Name>
          <Description>Registers a report in the reporting system</Description>
        </Event>
        <Event>
          <Name>show-collection</Name>
          <Description>Displays a collection of objects in a view</Description>
        </Event>
        <Event>
          <Name>click</Name>
          <Description>Handles click interactions on UI elements</Description>
        </Event>
      </CommonEvents>
    </Concept>

    <Concept>
      <Name>Component Structure</Name>
      <Description>
        Standard structure for Bento components with initialization and event handling.
      </Description>
      <Template>
        <![CDATA[
          Factory.register('componentName', function (sb) {

            // Private variables and functions
            var privateData = {};

            function privateFunction() {
              // Implementation
            }

            // View functions
            function renderView(ui, state, draw) {
              // UI rendering logic using DOM tools
              ui.makeNode(...);
              ui.patch();
            }

            // Data handling functions
            function loadData(callback) {
              sb.data.db.obj.getWhere(..., function(results) {
                // Process data
                callback(results);
              });
            }

            // Event handlers
            function handleEvent(data) {
              // Handle event data
            }

            // Public API
            return {
              // Event handlers exposed to the system
              handleExternalEvent: function(data) {
                // Implementation
              },

              // Initialization function (required)
              init: function() {
                // Register event listeners
                var listeners = {
                  'event-name': this.handleExternalEvent
                };

                sb.listen(listeners);

                // Register with navigation system
                sb.notify({
                  type: 'register-application',
                  data: {
                    navigationItem: {
                      // Configuration
                    }
                  }
                });
              }
            };
          });
        ]]>
      </Template>
    </Concept>
  </CoreConcepts>

  <CommonPatterns>
    <Pattern>
      <Name>Data Loading</Name>
      <Description>Standard pattern for loading data in Bento components</Description>
      <Example>
        <![CDATA[
          function loadData(state, onComplete) {
            // Show loading indicator
            loader(ui, 'Loading data...');

            // Get primary data
            sb.data.db.obj.getById('objectType', state.id, function(result) {

              // Update loading message
              loader(ui, 'Loading related data...');

              // Get related data
              sb.data.db.obj.getWhere('relatedType', {
                parent: result.id
              }, function(relatedData) {

                // Process data
                var processedData = {
                  main: result,
                  related: relatedData
                };

                // Pass processed data to callback
                onComplete(processedData);
              });
            });
          }
        ]]>
      </Example>
    </Pattern>

    <Pattern>
      <Name>View Rendering</Name>
      <Description>Pattern for rendering UI components</Description>
      <Example>
        <![CDATA[
          function renderView(ui, data) {
            // Clear existing content
            ui.empty();

            // Create container
            ui.makeNode('container', 'div', {
              css: 'ui segment'
            });

            // Add header
            ui.container.makeNode('header', 'div', {
              css: 'ui header',
              tag: 'h2',
              text: data.title
            });

            // Add content sections
            ui.container.makeNode('content', 'div', {
              css: 'ui basic segment'
            });

            // Render list items
            _.each(data.items, function(item) {
              ui.container.content.makeNode('item' + item.id, 'div', {
                css: 'ui item',
                text: item.name
              });
            });

            // Apply all DOM changes
            ui.patch();
          }
        ]]>
      </Example>
    </Pattern>

    <Pattern>
      <Name>Component Registration</Name>
      <Description>Registering a component with the navigation system</Description>
      <Example>
        <![CDATA[
          sb.notify({
            type: 'register-application',
            data: {
              navigationItem: {
                id: 'componentId',
                title: 'Component Title',
                icon: '<i class="fa fa-icon"></i>',
                views: [
                  {
                    id: 'mainView',
                    default: true,
                    type: 'custom',
                    title: 'Main View',
                    icon: '<i class="fa fa-view-icon"></i>',
                    dom: renderMainView
                  },
                  {
                    id: 'detailView',
                    type: 'object-view',
                    title: 'Detail View',
                    icon: 'detail',
                    dom: renderDetailView
                  }
                ]
              }
            }
          });
        ]]>
      </Example>
    </Pattern>

    <Pattern>
      <Name>Event Handling</Name>
      <Description>Standard pattern for handling events in Bento</Description>
      <Example>
        <![CDATA[
          // Register event listeners
          var listeners = {
            'custom-event': handleCustomEvent,
            'object-updated': handleObjectUpdated
          };

          sb.listen(listeners);

          // Event handler implementation
          function handleCustomEvent(data) {
            // Process event data
            var processedResult = processData(data);

            // Update UI or trigger another action
            updateUI(processedResult);

            // Optionally fire another event
            sb.notify({
              type: 'action-completed',
              data: {
                result: processedResult
              }
            });
          }
        ]]>
      </Example>
    </Pattern>
  </CommonPatterns>

  <CoreComponents>
    <Component>
      <Name>Notify System</Name>
      <Description>
        The central messaging system that enables communication between Bento components.
        It implements a publish/subscribe pattern.
      </Description>
      <Location>_SRC/notify/_factory/_core.js</Location>
      <Usage>
        <PublishEvent>
          <![CDATA[
            sb.notify({
              type: 'event-name',
              data: { /* Event data */ }
            });
          ]]>
        </PublishEvent>
        <SubscribeToEvent>
          <![CDATA[
            sb.listen({
              'event-name': eventHandler
            });
          ]]>
        </SubscribeToEvent>
      </Usage>
    </Component>

    <Component>
      <Name>Run Function</Name>
      <Description>
        A common pattern in Bento modules where a run function is used to execute an action
        or operation provided in an event. This pattern allows for dynamic behavior injection.
      </Description>
      <Example>
        <![CDATA[
          // In the module's public API
          run: function(data) {
            // Execute the function provided in the data
            if (data && typeof data.run === 'function') {
              data.run(data);
            }
          },

          // In the initialization
          init: function() {
            var listeners = {
              'component-run': this.run
            };

            sb.listen(listeners);
          }

          // Usage from another component
          ui.button.notify('click', {
            type: 'component-run',
            data: {
              run: function() {
                // Action to perform when clicked
                performAction();
              }
            }
          }, sb.moduleId);
        ]]>
      </Example>
    </Component>

    <Component>
      <Name>Bento DOM Tools</Name>
      <Description>
        A set of utilities for manipulating the DOM in a structured way.
        These tools provide an abstraction over direct DOM manipulation.
      </Description>
      <Location>_SRC/notify/_factory/domTools/</Location>
      <CoreFunctions>
        <Function>
          <Name>makeNode</Name>
          <Description>Creates a new DOM node with specified properties</Description>
          <Signature>parent.makeNode(id, type, options)</Signature>
          <Example>
            <![CDATA[
              ui.makeNode('container', 'div', {
                css: 'ui segment',
                text: 'Content goes here'
              });
            ]]>
          </Example>
        </Function>
        <Function>
          <Name>empty</Name>
          <Description>Removes all child nodes from an element</Description>
          <Example>ui.container.empty();</Example>
        </Function>
        <Function>
          <Name>patch</Name>
          <Description>Applies all pending DOM changes</Description>
          <Example>ui.patch();</Example>
        </Function>
      </CoreFunctions>
      <SpecialNodeTypes>
        <NodeType>
          <Name>div</Name>
          <Description>Standard container element</Description>
        </NodeType>
        <NodeType>
          <Name>lineBreak</Name>
          <Description>Creates vertical spacing</Description>
          <Example>ui.makeNode('br', 'lineBreak', { spaces: 2 });</Example>
        </NodeType>
        <NodeType>
          <Name>Custom factory types</Name>
          <Description>Additional node types defined in domTools directory</Description>
          <Examples>
            <Example>bsColumns: Bootstrap column layout</Example>
            <Example>bsTables: Bootstrap table</Example>
            <Example>charts: Data visualization</Example>
          </Examples>
        </NodeType>
      </SpecialNodeTypes>
    </Component>

    <Component>
      <Name>Database Access</Name>
      <Description>
        Utilities for accessing and manipulating data in the Bento system.
      </Description>
      <Location>_SRC/notify/_extensions/_database.js</Location>
      <CommonOperations>
        <Operation>
          <Name>getById</Name>
          <Description>Retrieves a single object by ID</Description>
          <Signature>sb.data.db.obj.getById(type, id, callback, fields)</Signature>
          <Example>
            <![CDATA[
              sb.data.db.obj.getById('companies', 123, function(company) {
                // Process company data
              }, {
                name: true,
                contact_info: {
                  email: true,
                  phone: true
                }
              });
            ]]>
          </Example>
        </Operation>
        <Operation>
          <Name>getWhere</Name>
          <Description>Retrieves objects matching specified conditions</Description>
          <Signature>sb.data.db.obj.getWhere(type, conditions, callback, fields)</Signature>
          <Example>
            <![CDATA[
              sb.data.db.obj.getWhere('invoices', {
                status: 'unpaid',
                due_date: {
                  lt: moment().format('YYYY-MM-DD')
                }
              }, function(overdue) {
                // Process overdue invoices
              });
            ]]>
          </Example>
        </Operation>
        <Operation>
          <Name>create</Name>
          <Description>Creates a new object</Description>
          <Signature>sb.data.db.obj.create(type, data, callback)</Signature>
        </Operation>
        <Operation>
          <Name>update</Name>
          <Description>Updates an existing object</Description>
          <Signature>sb.data.db.obj.update(type, id, data, callback)</Signature>
        </Operation>
      </CommonOperations>
    </Component>
  </CoreComponents>

  <MergeService>
    <Overview>
      <Description>
        The Merge Service is a Node.js microservice that handles document template processing
        using Mustache templating engine. It processes merge tags and generates final documents.
      </Description>
      <Location>_SRC/merge/</Location>
      <Port>8084</Port>
    </Overview>

    <Architecture>
      <CoreModule>_SRC/merge/src/modules/core-module.js</CoreModule>
      <TagLibrary>_SRC/merge/src/lib/tags/</TagLibrary>
      <MainEndpoint>/mergeService</MainEndpoint>
    </Architecture>

    <Integration>
      <PHPService>_SRC/pagoda/services/MergeService.php</PHPService>
      <MergeEndpoint>_SRC/pagoda/merge.php</MergeEndpoint>
      <Usage>
        <![CDATA[
          // From PHP backend
          $response = $pgObjects->mergeEndpoint($payload);

          // Payload structure
          {
            "contextId": "12345",
            "templateId": "67890",
            "templateHtml": "<h1>{{today}}</h1>",
            "mergeVars": {...},
            "authData": {
              "varToken": "...",
              "varInstance": "...",
              "varPagodaAPIKey": "..."
            }
          }
        ]]>
      </Usage>
    </Integration>

    <MergeTags>
      <Description>
        Merge tags are processed dynamically by loading functions from the tags directory.
        Each tag corresponds to a JavaScript module that processes the tag replacement.
      </Description>
      <TagProcessing>
        <![CDATA[
          // Tag detection pattern
          const pattern = /{\s*(\w+?)\s*}/g;

          // Dynamic function loading
          function getMergeFunction(tagName) {
            var filePath = path.join(libDir, tagName + '.js');
            return require(filePath)[tagName];
          }
        ]]>
      </TagProcessing>
    </MergeTags>
  </MergeService>

  <InventorySystem>
    <Overview>
      <Description>
        Bento implements a complex three-tier inventory management system specifically
        designed for catering/event management with nested choice structures.
      </Description>
    </Overview>

    <ObjectHierarchy>
      <Level1>
        <Name>inventory_billable_combination_categories</Name>
        <Purpose>Top-level categorization containers</Purpose>
        <Example>
          <![CDATA[
            {
              "id": ********,
              "name": "Food - 2025 Pricing (*)",
              "object_bp_type": "inventory_billable_combination_categories",
              "chart_of_account": 1122843,
              "default_pricing_option": "price_per_person"
            }
          ]]>
        </Example>
      </Level1>

      <Level2>
        <Name>inventory_billable_combinations</Name>
        <Purpose>Main menu items/packages</Purpose>
        <Structure>
          <![CDATA[
            {
              "id": ********,
              "name": "Buffet Entree* (*)",
              "category": ********,
              "items": [
                {
                  "id": 1,
                  "inventory_group": ********,
                  "qty": {"quantity": 1, "unit_type": "servings"},
                  "choices": []
                }
              ],
              "price_per_person": 5800
            }
          ]]>
        </Structure>
      </Level2>

      <Level3>
        <Name>inventory_billable_groups</Name>
        <Purpose>Component building blocks and choice lists</Purpose>
        <NestedStructure>
          <![CDATA[
            {
              "id": 20434630,
              "name": "Salad Choice List (*)",
              "category": 20435087,
              "items": [
                {
                  "id": 1,
                  "name": "Salad Choice Option",
                  "max_selections": 1,
                  "choices": [
                    {
                      "id": 13,
                      "inventory_group": 6225382,
                      "additional_price": 0
                    }
                  ]
                }
              ]
            }
          ]]>
        </NestedStructure>
      </Level3>
    </ObjectHierarchy>

    <BEOSystem>
      <Description>
        BEO (Banquet Event Order) merge tags parse project Menu data and render it
        in documents as formatted line items with nested choice structures.
      </Description>
      <Implementation>
        <Location>public_html/application/libraries/bento_merge_tags.php</Location>
        <Function>generateInfinityBEOMergeTag()</Function>
      </Implementation>
      <CategoryFiltering>
        <![CDATA[
          // BEO merge tags support category filtering
          {{Menu BEO}}(8277129,6040760,...)

          // Multiple versions available:
          // - 'Menu BEO' (standard)
          // - 'Infinity Menu BEO' (Infinity instance)
          // - 'generateDreamBEOMergeTag' (Dream Catering instance)
        ]]>
      </CategoryFiltering>
    </BEOSystem>
  </InventorySystem>

  <DataConventions>
    <MonetaryValues>
      <Description>Dollar values stored as cents (e.g., $100.00 = 10000)</Description>
    </MonetaryValues>

    <DateFormats>
      <ApplicationFormat>MM/DD/YY hh:mm:ss AM/PM</ApplicationFormat>
      <DatabaseFormat>TIMESTAMP DEFAULT NOW()</DatabaseFormat>
      <SpecialCases>
        <Case>
          <Field>date_booked</Field>
          <DefaultValue>'NULL' (string)</DefaultValue>
          <Note>PHP null checks must account for both actual null and string 'NULL'</Note>
        </Case>
      </SpecialCases>
    </DateFormats>

    <DuplicationSystem>
      <NamingConvention>
        <Original>Buffet Entree</Original>
        <Duplicated>Buffet Entree (*)</Duplicated>
      </NamingConvention>
      <DataSourceTracking>
        <Property>data_source</Property>
        <Purpose>Track relationship between original and duplicate records</Purpose>
      </DataSourceTracking>
    </DuplicationSystem>
  </DataConventions>

  <APIEndpoints>
    <Structure>
      <ReadEndpoint>_SRC/pagoda/_get.php</ReadEndpoint>
      <WriteEndpoint>_SRC/pagoda/_post.php</WriteEndpoint>
      <MergeEndpoint>_SRC/pagoda/merge.php</MergeEndpoint>
    </Structure>

    <URLPatterns>
      <Standard>/api/_get.php?do=METHOD&amp;pagodaAPIKey=INSTANCE</Standard>
      <Service>/api/_get.php?do=METHOD&amp;service=SERVICE&amp;pagodaAPIKey=INSTANCE</Service>
      <Important>API endpoint files must include actual PHP filename in URL path</Important>
    </URLPatterns>

    <Authentication>
      <TokenHeader>bento-token</TokenHeader>
      <InstanceKey>pagodaAPIKey</InstanceKey>
      <CookieSystem>Session-based authentication with series validation</CookieSystem>
    </Authentication>
  </APIEndpoints>

  <BestPractices>
    <Development>
      <Practice>
        <Name>Code Conservation</Name>
        <Description>Work within existing architecture, avoid creating new files</Description>
        <Guidelines>
          <Guideline>Use existing files and patterns</Guideline>
          <Guideline>Make surgical, precise changes only</Guideline>
          <Guideline>Clean up dead code during legitimate changes</Guideline>
          <Guideline>Preserve working systems</Guideline>
        </Guidelines>
      </Practice>

      <Practice>
        <Name>Database Operations</Name>
        <Description>Follow established patterns for data access</Description>
        <Guidelines>
          <Guideline>Use databaseConnection.obj methods consistently</Guideline>
          <Guideline>Handle both object references and ID arrays</Guideline>
          <Guideline>Implement proper error handling for async operations</Guideline>
          <Guideline>Use field selection to optimize queries</Guideline>
        </Guidelines>
      </Practice>

      <Practice>
        <Name>Multi-Tenant Considerations</Name>
        <Description>Ensure proper instance isolation</Description>
        <Guidelines>
          <Guideline>Always use instance-aware database connections</Guideline>
          <Guideline>Validate user permissions for cross-instance access</Guideline>
          <Guideline>Use proper instance configuration in API calls</Guideline>
          <Guideline>Test with multiple instance configurations</Guideline>
        </Guidelines>
      </Practice>

      <Practice>
        <Name>DOM Manipulation</Name>
        <Description>Use Bento DOM tools effectively</Description>
        <Guidelines>
          <Guideline>Use sb.dom.makeNode for DOM creation</Guideline>
          <Guideline>Call ui.patch() to apply DOM changes</Guideline>
          <Guideline>Use meaningful node IDs for debugging</Guideline>
          <Guideline>Batch DOM updates for performance</Guideline>
        </Guidelines>
      </Practice>

      <Practice>
        <Name>PDF Generation</Name>
        <Description>Use sb.data.makePDF for document generation</Description>
        <Guidelines>
          <Guideline>Convert HTML string input, not jQuery objects</Guideline>
          <Guideline>Use appropriate mode (I, D, F, S) for output</Guideline>
          <Guideline>Collect HTML using jQuery selectors when needed</Guideline>
          <Guideline>Avoid window.print() - use PDF generation instead</Guideline>
        </Guidelines>
      </Practice>
    </Development>

    <Testing>
      <LocalDevelopment>
        <Instance>rickyvoltz</Instance>
        <DatabaseMigration>
          <Process>
            <Step>Monitor XHR requests to collect production IDs</Step>
            <Step>Use databaseConnection.getById() to fetch records</Step>
            <Step>Download as file for local import</Step>
            <Step>Run SQL migration preserving original IDs</Step>
          </Process>
        </DatabaseMigration>
      </LocalDevelopment>

      <ProductionTesting>
        <Note>Local dev environment lacks production data</Note>
        <Approach>Use email-based debugging logs for production systems</Approach>
        <BEOTesting>Must be done in production environment</BEOTesting>
      </ProductionTesting>
    </Testing>
  </BestPractices>

  <TroubleshootingGuide>
    <CommonIssues>
      <Issue>
        <Problem>Empty BEO merge tag results</Problem>
        <Cause>Instance configuration or database connection issues</Cause>
        <Solution>Verify instance-specific database path setup</Solution>
      </Issue>

      <Issue>
        <Problem>Broken inventory references</Problem>
        <Cause>Shallow cloning during duplication process</Cause>
        <Solution>Update nested references to point to duplicated groups</Solution>
      </Issue>

      <Issue>
        <Problem>DOM manipulation not working</Problem>
        <Cause>Missing ui.patch() call or incorrect makeNode usage</Cause>
        <Solution>Ensure proper DOM tool usage and patch application</Solution>
      </Issue>

      <Issue>
        <Problem>Database query returns empty results</Problem>
        <Cause>Instance mismatch or incorrect object_bp_type</Cause>
        <Solution>Verify instance configuration and object type consistency</Solution>
      </Issue>
    </CommonIssues>
  </TroubleshootingGuide>

  <DevelopmentWorkflow>
    <GitWorkflow>
      <Branches>
        <Development>dev</Development>
        <Production>master</Production>
      </Branches>
      <Process>
        <Step>Test changes on dev branch</Step>
        <Step>Create focused, small commits</Step>
        <Step>Never commit without explicit instruction</Step>
        <Step>Merge to master only after thorough testing</Step>
      </Process>
    </GitWorkflow>

    <CodeFormatting>
      <Tool>Prettier for JavaScript</Tool>
      <Settings>
        <Setting>prettier.bracketSameLine: true</Setting>
        <Setting>prettier.tabWidth: 4</Setting>
      </Settings>
    </CodeFormatting>
  </DevelopmentWorkflow>
</BentoPlatform>
