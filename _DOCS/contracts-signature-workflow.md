# Contract Signature Workflow Documentation

This document maps the complete signature workflow for contracts in the Bento system, including both signature request and document signing processes.

## Request Signature Workflow (T6 Research)

### Entry Point
**Location**: `/Users/<USER>/Infinity/bento/_SRC/notify/_components/_bizdev/contracts.js` (lines 6214-6553)
**Function**: `actions.requestSignature`

### Complete Flow

#### Phase 1: Contact Information Gathering (Lines 6237-6306)
- Retrieves main contact via `sb.data.db.obj.getById("contacts", obj.main_contact.id)`
- Extracts primary email addresses from contact_info array
- Builds additional contacts list from `obj.additional_contacts`
- Creates email recipient selection list with checkboxes

#### Phase 2: Email Template Processing (Lines 6308-6394)
- Calls `checkSettingsObject()` function (lines 672-702) to get contract system settings
- Retrieves default email templates:
  - `signature_disclaimer` (default disclaimer text)
  - `request_email` (email body template)  
  - `request_email_subject` (subject line template)
- Processes merge variables including:
  - `*|first_name|*`, `*|last_name|*`
  - `{{PROPOSAL.NAME}}`, `{{PROJECT.NAME}}`
  - `{{DOCUMENT.NAME}}`, `*|contract_name|*`
  - `*|venue|*`, `*|start_date|*`
  - `*|manager_first_name|*`, `*|manager_last_name|*`

#### Phase 3: Email Composition Modal (Lines 6395-6451)
- Triggers `sb.notify()` with type `"show-compose-form"`
- Creates signature link: `<a href="[sb.url]/app/contracts#?&i=[instance]&wid=[obj.id]">CLICK HERE TO VIEW</a>`
- Sets up email form with pre-populated recipient, subject, and body

### Database Updates on Email Send

#### Contract Object Updates (Lines 6417-6423)
```javascript
sb.data.db.obj.update("contracts", {
  id: obj.id,
  html_string: html,        // Generated by createMergedHTML()
  status: "Out For Signature"
})
```

#### HTML Generation Process (Line 6416)
- Calls `createMergedHTML(obj, function(html){...})` (function starts at line 784)
- Processes merge tags for contact, company, project, inventory data
- Generates finalized HTML string for the contract

#### Notes Record Creation (Lines 6425-6445)
```javascript
var noteObj = {
  type_id: obj.id,
  type: "contracts", 
  note: "Contract has been emailed to the client.",
  note_type: 0,
  author: +sb.data.cookie.userId,
  notifyUsers: []
};
sb.data.db.obj.create("notes", noteObj, callback)
```

### Email/Notification Flow
- **Email Composition**: `/Users/<USER>/Infinity/bento/_SRC/notify/_components/_core/emails.js`
- **Backend Processing**: `/Users/<USER>/Infinity/bento/_SRC/pagoda/_communications.php`
- Email records include: `type: "contracts"`, `type_id: [contract_id]`, mandrill tracking

---

## Document Signing Workflow (T7 Research)

### Portal Contract Display
**Entry Point**: Clients access via URL format: `https://bento.infinityhospitality.net/app/contracts#?&i={instance}&wid={contractId}`

**Display Process** (lines 7903-8000):
- `signContractView()` function handles portal display
- Shows 4-step process: Electronic Signature Disclaimer → Review → Sign → Done
- Contract status determines view:
  - If `obj.status == "Signed"` → shows `signedView()`
  - Otherwise → shows signing interface

### Signing Interface (Lines 8000-8100)
**UI Components**:
- Electronic signature disclaimer form
- Name and email input fields (lines 8025-8037)
- "Accept & Continue" button triggers consent process

**Consent Process** (lines 8050-8100):
- Validates user name/email input
- Updates contract with signer information:
  ```javascript
  obj.signer_email = email;
  obj.signer_name = fullName;
  ```

### Signature Capture (Lines 8124-8400)

**Canvas Signature** (lines 8260-8330):
- Uses SignaturePad library on HTML5 canvas
- Canvas element: `<canvas id="signature-pad" class="signature-pad" width=700 height=200>`
- Captures signature as base64 data URL

**Text Signature** (lines 8988-9050):
- Alternative signature method using typed name
- Converts text to image blob

**File Storage Process** (lines 8396-8522):
- Converts signature to PNG blob via `dataURItoBlob()`
- File metadata: `contract-signature-{contractId}.png`
- Uploads via `sb.data.files.upload()` API
- Links file to contract via `signatures` field

### Database Updates on Signing
**Location**: `_app.php` lines 11403-11502 (`signContract()` function)

```php
$updated = $this->pgObjects->update(
    'contracts',
    [
        'id' => $contract['id'],
        'signer_ip' => $request['signer_ip'],
        'signer_name' => $request['signer_name'], 
        'signer_email' => $request['signer_email'],
        'signed_on' => $request['signed_on'],
        'status' => 'Signed',
        'html_string' => $request['html_string'],
        'signatures' => $savedFile.id
    ]
);
```

### Document Locking Process (Lines 8506-8600)

**HTML Processing Steps**:
1. **Replace Signature Placeholders**: 
   ```javascript
   contract.html_string_merged = contract.html_string_merged.replace(
       new RegExp("PLEASE SIGN HERE", "g"), 
       "{{PLEASE SIGN HERE}}"
   );
   ```

2. **Embed Signature Image**:
   ```javascript
   contract.html_string_merged = contract.html_string.replace(
       new RegExp("{{PLEASE SIGN HERE}}"),
       '<img width="300px" src="' + sb.data.files.getURL(contract.signatures) + 
       '"> - ' + fullName + " @ " + moment().local().format("M/DD/YYYY h:mm:ss a")
   );
   ```

3. **Remove Signing UI**:
   ```javascript
   contract.html_string_merged = contract.html_string_merged.replace(
       new RegExp('<a id="startSignature" class="ui green left floated compact button">Click To Sign</a>'),
       ""
   );
   ```

**Document Locking Mechanism**:
- The `html_string` field stores the final "locked" HTML
- Signature placeholders permanently replaced with actual signature images
- Interactive elements ("Click To Sign" buttons) removed
- Document becomes read-only display

### Status Progression
1. **"Unsigned"** → Initial state
2. **"Out For Signature"** → Set when signature requested
3. **"Signing In Process"** → Set when user begins signing
4. **"Signed"** → Final state after completion

### Completion Actions (Lines 8620-8850)

**Automated Actions After Signing**:
- **Email Notifications**: To project managers and signer
- **Note Creation**: Creates log entry in `notes` table for audit trail
- **System Notifications**: Alerts relevant staff
- **Post-Signature Actions**: Payment portal redirect if configured
- **Project State Transitions**: Triggers project type-specific state changes

### Data Storage Details

**Signature Files**:
- **Location**: Digital Ocean Spaces (production)
- **Format**: PNG images
- **Naming**: `contract-signature-{contractId}.png`
- **Database**: `file_meta_data` table with ID stored in `contracts.signatures`

**Contract HTML Storage**:
- **Original Template**: Stored in `html_string` field initially
- **Signed Version**: Final merged HTML with embedded signatures overwrites `html_string`
- **Merge Tags**: Processed during signing to embed signature images permanently

---

## Key Findings for Contract Duplication

### Current Challenge
When duplicating signed contracts, the system needs to create a fresh, unsigned version from the original template rather than trying to "reset" the complex signed state.

### Solution Approach
Instead of complex signature reset logic, use template-based duplication:
1. Preserve original template ID in `data_source_id`
2. Use `createFromTemplate()` with original template
3. Maintain project context and relationships
4. Apply proper versioning with `data_source` property

### Critical Reset Requirements (If Needed)
For any signature reset functionality:
- **Status Fields**: Reset to "Unsigned", clear signer details
- **HTML Reset**: Restore `{{PLEASE SIGN HERE}}` merge tags, remove embedded signatures
- **File References**: Clear `signatures` field references
- **Related Records**: Handle signing-related notes and email records

---

*Documentation generated from T6/T7 research findings - July 30, 2025*